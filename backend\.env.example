# ================================
# 🔧 CONFIGURATION ENVIRONNEMENT
# ================================

# Application
NODE_ENV=development
PORT=5000
APP_NAME=SassouFashion
APP_URL=http://localhost:3000
API_URL=http://localhost:5000

# ================================
# 🗄️ BASE DE DONNÉES
# ================================
DATABASE_URL="postgresql://username:password@localhost:5432/sassoufashion?schema=public"

# ================================
# 🔐 SÉCURITÉ
# ================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# ================================
# 📧 EMAIL CONFIGURATION
# ================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=SassouFashion

# ================================
# 📱 SMS CONFIGURATION (TWILIO)
# ================================
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# ================================
# ☁️ CLOUDINARY (IMAGES)
# ================================
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# ================================
# 💳 PAIEMENTS
# ================================

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# PayPal
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox

# D17 (Tunisie)
D17_API_KEY=your-d17-api-key
D17_SECRET_KEY=your-d17-secret-key
D17_ENVIRONMENT=sandbox

# e-dinar (Tunisie)
EDINAR_MERCHANT_ID=your-edinar-merchant-id
EDINAR_SECRET_KEY=your-edinar-secret-key
EDINAR_ENVIRONMENT=test

# ================================
# 📦 LIVRAISON
# ================================

# Poste Tunisienne
POSTE_API_KEY=your-poste-api-key
POSTE_USERNAME=your-poste-username
POSTE_PASSWORD=your-poste-password

# Aramex
ARAMEX_USERNAME=your-aramex-username
ARAMEX_PASSWORD=your-aramex-password
ARAMEX_ACCOUNT_NUMBER=your-aramex-account
ARAMEX_ACCOUNT_PIN=your-aramex-pin
ARAMEX_ACCOUNT_ENTITY=your-aramex-entity
ARAMEX_ACCOUNT_COUNTRY_CODE=TN

# ================================
# 🔴 REDIS (CACHE & SESSIONS)
# ================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# ================================
# 📊 MONITORING & LOGS
# ================================
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Sentry (Error Tracking)
SENTRY_DSN=your-sentry-dsn

# ================================
# 🌐 CORS & SECURITY
# ================================
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ================================
# 📱 SOCIAL MEDIA INTEGRATION
# ================================

# WhatsApp Business API
WHATSAPP_TOKEN=your-whatsapp-token
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id
WHATSAPP_VERIFY_TOKEN=your-verify-token

# Facebook Messenger
FACEBOOK_PAGE_ACCESS_TOKEN=your-page-access-token
FACEBOOK_VERIFY_TOKEN=your-facebook-verify-token

# ================================
# 🔧 AUTRES CONFIGURATIONS
# ================================
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp
SESSION_SECRET=your-session-secret-key
COOKIE_SECURE=false
COOKIE_SAME_SITE=lax

const express = require('express');
const { body, param, query } = require('express-validator');
const { authenticate, authorize } = require('../middleware/auth');
const orderController = require('../controllers/orderController');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Validation middleware
const validateCreateOrder = [
  body('items')
    .isArray({ min: 1 })
    .withMessage('Au moins un article requis'),
  body('items.*.variantId')
    .isUUID()
    .withMessage('ID de variante invalide'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantité invalide'),
  body('addressId')
    .isUUID()
    .withMessage('ID d\'adresse invalide'),
  body('paymentMethod')
    .isIn(['CARD', 'D17', 'E_DINAR', 'PAYPAL', 'CASH_ON_DELIVERY'])
    .withMessage('Méthode de paiement invalide'),
  body('shippingMethod')
    .optional()
    .isIn(['standard', 'express'])
    .withMessage('Méthode de livraison invalide'),
  body('loyaltyPointsUsed')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Points de fidélité invalides')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Numéro de page invalide'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite invalide (1-100)'),
  query('status')
    .optional()
    .isIn(['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED'])
    .withMessage('Statut invalide')
];

const validateId = [
  param('id')
    .isUUID()
    .withMessage('ID de commande invalide')
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    return next(new AppError(errorMessages.join(', '), 400, 'VALIDATION_ERROR'));
  }
  next();
};

// All routes require authentication
router.use(authenticate);

// User order routes
router.post('/', validateCreateOrder, handleValidationErrors, orderController.createOrder);
router.get('/', validatePagination, handleValidationErrors, orderController.getUserOrders);
router.get('/:id', validateId, handleValidationErrors, orderController.getOrder);

// Admin order routes
router.get('/admin/all', authorize('ADMIN', 'EMPLOYEE'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, status, search, startDate, endDate } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    const where = {
      ...(status && { status }),
      ...(startDate && endDate && {
        createdAt: {
          gte: new Date(startDate),
          lte: new Date(endDate)
        }
      }),
      ...(search && {
        OR: [
          { orderNumber: { contains: search, mode: 'insensitive' } },
          { user: {
            OR: [
              { firstName: { contains: search, mode: 'insensitive' } },
              { lastName: { contains: search, mode: 'insensitive' } },
              { email: { contains: search, mode: 'insensitive' } }
            ]
          }}
        ]
      })
    };

    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true, email: true }
          },
          items: {
            include: {
              variant: {
                include: {
                  product: {
                    select: { id: true, name: true }
                  }
                }
              }
            }
          },
          payment: {
            select: { method: true, status: true, paidAt: true }
          },
          shipping: {
            select: { method: true, trackingNumber: true, shippedAt: true, deliveredAt: true }
          }
        }
      }),
      prisma.order.count({ where })
    ]);

    const totalPages = Math.ceil(total / take);

    res.json({
      status: 'success',
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: take
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;

const express = require('express');
const { body, param, query } = require('express-validator');
const { authenticate, authorize } = require('../middleware/auth');
const stockService = require('../services/stockService');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Validation middleware
const validateStockUpdate = [
  body('quantity')
    .isInt({ min: 0 })
    .withMessage('Quantité invalide'),
  body('type')
    .isIn(['in', 'out', 'adjustment'])
    .withMessage('Type de mouvement invalide'),
  body('reason')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Raison trop longue'),
  body('reference')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Référence trop longue')
];

const validateBulkUpdate = [
  body('updates')
    .isArray({ min: 1 })
    .withMessage('Liste de mises à jour requise'),
  body('updates.*.variantId')
    .isUUID()
    .withMessage('ID de variante invalide'),
  body('updates.*.quantity')
    .isInt({ min: 0 })
    .withMessage('Quantité invalide'),
  body('updates.*.type')
    .isIn(['in', 'out', 'adjustment'])
    .withMessage('Type de mouvement invalide')
];

const validateId = [
  param('id')
    .isUUID()
    .withMessage('ID invalide')
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    return next(new AppError(errorMessages.join(', '), 400, 'VALIDATION_ERROR'));
  }
  next();
};

// All routes require authentication and admin/employee role
router.use(authenticate);
router.use(authorize('ADMIN', 'EMPLOYEE'));

// @desc    Get stock summary
// @route   GET /api/stock/summary
// @access  Private (Admin/Employee)
router.get('/summary', async (req, res, next) => {
  try {
    const summary = await stockService.getStockSummary();

    res.json({
      status: 'success',
      data: { summary }
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Get low stock products
// @route   GET /api/stock/low-stock
// @access  Private (Admin/Employee)
router.get('/low-stock', async (req, res, next) => {
  try {
    const { threshold } = req.query;
    const lowStockProducts = await stockService.getLowStockProducts(
      threshold ? parseInt(threshold) : undefined
    );

    res.json({
      status: 'success',
      data: { products: lowStockProducts }
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Get stock movements
// @route   GET /api/stock/movements
// @access  Private (Admin/Employee)
router.get('/movements', async (req, res, next) => {
  try {
    const filters = {
      productId: req.query.productId,
      variantId: req.query.variantId,
      type: req.query.type,
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 50
    };

    const result = await stockService.getStockMovements(filters);

    res.json({
      status: 'success',
      data: result
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Update stock for variant
// @route   POST /api/stock/variants/:id/update
// @access  Private (Admin/Employee)
router.post('/variants/:id/update',
  validateId,
  validateStockUpdate,
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { id } = req.params;
      const { quantity, type, reason, reference } = req.body;

      const updatedVariant = await stockService.updateStock(
        id,
        quantity,
        type,
        reason,
        reference
      );

      res.json({
        status: 'success',
        message: 'Stock mis à jour avec succès',
        data: { variant: updatedVariant }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @desc    Bulk update stock
// @route   POST /api/stock/bulk-update
// @access  Private (Admin/Employee)
router.post('/bulk-update',
  validateBulkUpdate,
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { updates } = req.body;

      const results = await stockService.bulkUpdateStock(updates);

      res.json({
        status: 'success',
        message: `${results.length} stocks mis à jour avec succès`,
        data: { variants: results }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @desc    Reserve stock
// @route   POST /api/stock/reserve
// @access  Private (Admin/Employee)
router.post('/reserve',
  [
    body('items')
      .isArray({ min: 1 })
      .withMessage('Liste d\'articles requise'),
    body('items.*.variantId')
      .isUUID()
      .withMessage('ID de variante invalide'),
    body('items.*.quantity')
      .isInt({ min: 1 })
      .withMessage('Quantité invalide')
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { items } = req.body;

      const reservations = await stockService.reserveStock(items);

      res.status(201).json({
        status: 'success',
        message: 'Stock réservé avec succès',
        data: { reservations }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @desc    Release stock reservation
// @route   DELETE /api/stock/reservations/:id
// @access  Private (Admin/Employee)
router.delete('/reservations/:id',
  validateId,
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { id } = req.params;

      await stockService.releaseReservation(id);

      res.json({
        status: 'success',
        message: 'Réservation libérée avec succès'
      });

    } catch (error) {
      next(error);
    }
  }
);

// @desc    Cleanup expired reservations
// @route   POST /api/stock/cleanup-reservations
// @access  Private (Admin)
router.post('/cleanup-reservations', authorize('ADMIN'), async (req, res, next) => {
  try {
    const count = await stockService.cleanupExpiredReservations();

    res.json({
      status: 'success',
      message: `${count} réservations expirées nettoyées`,
      data: { cleanedCount: count }
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Set stock thresholds
// @route   POST /api/stock/thresholds
// @access  Private (Admin)
router.post('/thresholds',
  authorize('ADMIN'),
  [
    body('lowStock')
      .isInt({ min: 0 })
      .withMessage('Seuil de stock faible invalide'),
    body('outOfStock')
      .isInt({ min: 0 })
      .withMessage('Seuil de rupture de stock invalide')
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { lowStock, outOfStock } = req.body;

      stockService.setThresholds(lowStock, outOfStock);

      res.json({
        status: 'success',
        message: 'Seuils de stock mis à jour',
        data: { lowStock, outOfStock }
      });

    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;

const { PrismaClient } = require('@prisma/client');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const emailService = require('../services/emailService');
const loyaltyService = require('../services/loyaltyService');
const socketService = require('../services/socketService');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

const prisma = new PrismaClient();

class PaymentController {
  // @desc    Process payment
  // @route   POST /api/payments/process
  // @access  Private
  async processPayment(req, res, next) {
    try {
      const { orderId, paymentMethod, paymentData } = req.body;

      // Get order
      const order = await prisma.order.findFirst({
        where: {
          id: orderId,
          userId: req.user.id,
          status: 'PENDING'
        },
        include: {
          user: {
            select: { id: true, firstName: true, lastName: true, email: true }
          },
          items: {
            include: {
              variant: {
                include: {
                  product: {
                    select: { id: true, name: true }
                  }
                }
              }
            }
          }
        }
      });

      if (!order) {
        return next(new AppError('Commande non trouvée ou déjà traitée', 404, 'ORDER_NOT_FOUND'));
      }

      let paymentResult;

      switch (paymentMethod) {
        case 'CARD':
          paymentResult = await this.processCardPayment(order, paymentData);
          break;
        case 'D17':
          paymentResult = await this.processD17Payment(order, paymentData);
          break;
        case 'E_DINAR':
          paymentResult = await this.processEDinarPayment(order, paymentData);
          break;
        case 'PAYPAL':
          paymentResult = await this.processPayPalPayment(order, paymentData);
          break;
        case 'CASH_ON_DELIVERY':
          paymentResult = await this.processCashOnDelivery(order);
          break;
        default:
          return next(new AppError('Méthode de paiement non supportée', 400, 'UNSUPPORTED_PAYMENT_METHOD'));
      }

      // Update order with payment info
      const updatedOrder = await prisma.$transaction(async (tx) => {
        // Create payment record
        const payment = await tx.payment.create({
          data: {
            orderId: order.id,
            method: paymentMethod,
            status: paymentResult.status,
            amount: order.total,
            currency: order.currency,
            transactionId: paymentResult.transactionId,
            paidAt: paymentResult.status === 'PAID' ? new Date() : null
          }
        });

        // Update order status
        const newOrderStatus = paymentResult.status === 'PAID' ? 'CONFIRMED' : 'PENDING';
        const updatedOrder = await tx.order.update({
          where: { id: order.id },
          data: { 
            status: newOrderStatus,
            paymentId: payment.id
          },
          include: {
            user: {
              select: { id: true, firstName: true, lastName: true, email: true }
            },
            items: {
              include: {
                variant: {
                  include: {
                    product: {
                      select: { id: true, name: true }
                    }
                  }
                }
              }
            },
            payment: true
          }
        });

        // Award loyalty points if payment successful
        if (paymentResult.status === 'PAID') {
          try {
            await loyaltyService.awardPointsForOrder(
              order.userId,
              order.id,
              order.total
            );
          } catch (loyaltyError) {
            logger.error('Failed to award loyalty points', {
              orderId: order.id,
              error: loyaltyError.message
            });
          }
        }

        return updatedOrder;
      });

      // Send notifications if payment successful
      if (paymentResult.status === 'PAID') {
        // Send confirmation email
        try {
          await emailService.sendOrderConfirmationEmail(
            updatedOrder.user.email,
            updatedOrder,
            updatedOrder.user.firstName
          );
        } catch (emailError) {
          logger.error('Failed to send payment confirmation email', {
            orderId: updatedOrder.id,
            error: emailError.message
          });
        }

        // Notify user via socket
        socketService.sendOrderNotification(
          updatedOrder.userId,
          updatedOrder.id,
          'CONFIRMED'
        );
      }

      logger.logBusiness('Payment processed', {
        orderId: order.id,
        paymentMethod,
        status: paymentResult.status,
        amount: order.total,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: paymentResult.status === 'PAID' ? 'Paiement effectué avec succès' : 'Paiement en cours de traitement',
        data: {
          order: updatedOrder,
          payment: paymentResult
        }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Create Stripe payment intent
  // @route   POST /api/payments/stripe/intent
  // @access  Private
  async createStripePaymentIntent(req, res, next) {
    try {
      const { orderId } = req.body;

      const order = await prisma.order.findFirst({
        where: {
          id: orderId,
          userId: req.user.id,
          status: 'PENDING'
        }
      });

      if (!order) {
        return next(new AppError('Commande non trouvée', 404, 'ORDER_NOT_FOUND'));
      }

      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(order.total * 100), // Convert to cents
        currency: 'usd', // Stripe requires USD for international cards
        metadata: {
          orderId: order.id,
          orderNumber: order.orderNumber,
          userId: req.user.id
        }
      });

      res.json({
        status: 'success',
        data: {
          clientSecret: paymentIntent.client_secret,
          paymentIntentId: paymentIntent.id
        }
      });

    } catch (error) {
      next(error);
    }
  }

  // Payment method implementations
  async processCardPayment(order, paymentData) {
    try {
      // Confirm payment intent with Stripe
      const paymentIntent = await stripe.paymentIntents.confirm(paymentData.paymentIntentId);

      return {
        status: paymentIntent.status === 'succeeded' ? 'PAID' : 'FAILED',
        transactionId: paymentIntent.id,
        details: paymentIntent
      };
    } catch (error) {
      logger.error('Stripe payment failed', {
        orderId: order.id,
        error: error.message
      });
      return {
        status: 'FAILED',
        transactionId: null,
        error: error.message
      };
    }
  }

  async processD17Payment(order, paymentData) {
    // D17 payment integration (mock implementation)
    // In real implementation, integrate with D17 API
    try {
      const { phoneNumber } = paymentData;

      // Mock D17 API call
      const d17Response = await this.mockD17API({
        amount: order.total,
        phoneNumber,
        orderId: order.id
      });

      return {
        status: d17Response.success ? 'PAID' : 'FAILED',
        transactionId: d17Response.transactionId,
        details: d17Response
      };
    } catch (error) {
      logger.error('D17 payment failed', {
        orderId: order.id,
        error: error.message
      });
      return {
        status: 'FAILED',
        transactionId: null,
        error: error.message
      };
    }
  }

  async processEDinarPayment(order, paymentData) {
    // e-dinar payment integration (mock implementation)
    try {
      const { accountNumber, pin } = paymentData;

      // Mock e-dinar API call
      const eDinarResponse = await this.mockEDinarAPI({
        amount: order.total,
        accountNumber,
        pin,
        orderId: order.id
      });

      return {
        status: eDinarResponse.success ? 'PAID' : 'FAILED',
        transactionId: eDinarResponse.transactionId,
        details: eDinarResponse
      };
    } catch (error) {
      logger.error('e-dinar payment failed', {
        orderId: order.id,
        error: error.message
      });
      return {
        status: 'FAILED',
        transactionId: null,
        error: error.message
      };
    }
  }

  async processPayPalPayment(order, paymentData) {
    // PayPal payment integration (mock implementation)
    try {
      const { paypalOrderId } = paymentData;

      // Mock PayPal API call
      const paypalResponse = await this.mockPayPalAPI({
        paypalOrderId,
        amount: order.total
      });

      return {
        status: paypalResponse.success ? 'PAID' : 'FAILED',
        transactionId: paypalResponse.transactionId,
        details: paypalResponse
      };
    } catch (error) {
      logger.error('PayPal payment failed', {
        orderId: order.id,
        error: error.message
      });
      return {
        status: 'FAILED',
        transactionId: null,
        error: error.message
      };
    }
  }

  async processCashOnDelivery(order) {
    // Cash on delivery - no immediate payment
    return {
      status: 'PENDING',
      transactionId: `COD_${order.orderNumber}`,
      details: {
        method: 'Cash on Delivery',
        note: 'Payment will be collected upon delivery'
      }
    };
  }

  // Mock API implementations (replace with real integrations)
  async mockD17API(data) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock success/failure (90% success rate)
    const success = Math.random() > 0.1;
    
    return {
      success,
      transactionId: success ? `D17_${Date.now()}` : null,
      message: success ? 'Payment successful' : 'Payment failed'
    };
  }

  async mockEDinarAPI(data) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const success = Math.random() > 0.1;
    
    return {
      success,
      transactionId: success ? `EDINAR_${Date.now()}` : null,
      message: success ? 'Payment successful' : 'Payment failed'
    };
  }

  async mockPayPalAPI(data) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const success = Math.random() > 0.1;
    
    return {
      success,
      transactionId: success ? `PP_${Date.now()}` : null,
      message: success ? 'Payment successful' : 'Payment failed'
    };
  }
}

module.exports = new PaymentController();

// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// 👤 USER MANAGEMENT
// ================================

enum UserRole {
  CLIENT
  EMPLOYEE
  ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

model User {
  id          String     @id @default(cuid())
  email       String     @unique
  password    String
  firstName   String
  lastName    String
  phone       String?
  avatar      String?
  role        UserRole   @default(CLIENT)
  status      UserStatus @default(ACTIVE)
  isVerified  Boolean    @default(false)
  lastLoginAt DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  addresses     Address[]
  orders        Order[]
  reviews       Review[]
  cartItems     CartItem[]
  loyaltyPoints LoyaltyPoint[]
  supportTickets SupportTicket[]
  auditLogs     AuditLog[]
  notifications Notification[]

  @@map("users")
}

model Address {
  id          String  @id @default(cuid())
  userId      String
  type        String  @default("shipping") // shipping, billing
  firstName   String
  lastName    String
  company     String?
  address1    String
  address2    String?
  city        String
  state       String
  postalCode  String
  country     String  @default("TN")
  phone       String?
  isDefault   Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders Order[]

  @@map("addresses")
}

model LoyaltyPoint {
  id          String   @id @default(cuid())
  userId      String
  points      Int
  type        String   // earned, redeemed, expired
  description String
  orderId     String?
  expiresAt   DateTime?
  createdAt   DateTime @default(now())

  // Relations
  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  order Order? @relation(fields: [orderId], references: [id])

  @@map("loyalty_points")
}

// ================================
// 🛍️ PRODUCT MANAGEMENT
// ================================

enum ProductStatus {
  ACTIVE
  INACTIVE
  DRAFT
  ARCHIVED
}

model Category {
  id          String  @id @default(cuid())
  name        String
  slug        String  @unique
  description String?
  image       String?
  parentId    String?
  sortOrder   Int     @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

model Product {
  id          String        @id @default(cuid())
  name        String
  slug        String        @unique
  description String?
  shortDesc   String?
  sku         String        @unique
  categoryId  String
  brand       String?
  status      ProductStatus @default(DRAFT)
  isFeatured  Boolean       @default(false)
  weight      Float?
  dimensions  Json?         // {length, width, height}
  tags        String[]
  seoTitle    String?
  seoDesc     String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  category      Category        @relation(fields: [categoryId], references: [id])
  variants      ProductVariant[]
  images        ProductImage[]
  reviews       Review[]
  cartItems     CartItem[]
  stockMovements StockMovement[]
  promotions    ProductPromotion[]

  @@map("products")
}

model ProductVariant {
  id        String  @id @default(cuid())
  productId String
  sku       String  @unique
  size      String?
  color     String?
  material  String?
  price     Decimal @db.Decimal(10, 2)
  comparePrice Decimal? @db.Decimal(10, 2)
  costPrice Decimal? @db.Decimal(10, 2)
  stock     Int     @default(0)
  weight    Float?
  isDefault Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  product   Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]
  cartItems CartItem[]
  stockMovements StockMovement[]

  @@map("product_variants")
}

model ProductImage {
  id        String  @id @default(cuid())
  productId String
  url       String
  altText   String?
  sortOrder Int     @default(0)
  createdAt DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model StockMovement {
  id        String   @id @default(cuid())
  productId String
  variantId String?
  type      String   // in, out, adjustment
  quantity  Int
  reason    String?
  reference String?  // order_id, adjustment_id, etc.
  userId    String?
  createdAt DateTime @default(now())

  // Relations
  product Product         @relation(fields: [productId], references: [id])
  variant ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("stock_movements")
}

// ================================
// 🧾 ORDER MANAGEMENT
// ================================

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum PaymentMethod {
  CARD
  D17
  E_DINAR
  PAYPAL
  CASH_ON_DELIVERY
}

model Order {
  id            String        @id @default(cuid())
  orderNumber   String        @unique
  userId        String
  status        OrderStatus   @default(PENDING)
  subtotal      Decimal       @db.Decimal(10, 2)
  taxAmount     Decimal       @db.Decimal(10, 2) @default(0)
  shippingCost  Decimal       @db.Decimal(10, 2) @default(0)
  discountAmount Decimal      @db.Decimal(10, 2) @default(0)
  total         Decimal       @db.Decimal(10, 2)
  currency      String        @default("TND")
  notes         String?
  addressId     String
  couponId      String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  user          User           @relation(fields: [userId], references: [id])
  address       Address        @relation(fields: [addressId], references: [id])
  coupon        Coupon?        @relation(fields: [couponId], references: [id])
  items         OrderItem[]
  payment       Payment?
  shipping      Shipping?
  loyaltyPoints LoyaltyPoint[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  variantId String
  quantity  Int
  price     Decimal @db.Decimal(10, 2)
  total     Decimal @db.Decimal(10, 2)

  // Relations
  order   Order          @relation(fields: [orderId], references: [id], onDelete: Cascade)
  variant ProductVariant @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

model CartItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  variantId String
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product        @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([userId, variantId])
  @@map("cart_items")
}

model Payment {
  id            String        @id @default(cuid())
  orderId       String        @unique
  method        PaymentMethod
  status        PaymentStatus @default(PENDING)
  amount        Decimal       @db.Decimal(10, 2)
  currency      String        @default("TND")
  transactionId String?
  gatewayResponse Json?
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("payments")
}

model Shipping {
  id          String    @id @default(cuid())
  orderId     String    @unique
  method      String    // standard, express, pickup
  carrier     String?   // poste, aramex, etc.
  trackingNumber String?
  cost        Decimal   @db.Decimal(10, 2)
  estimatedDelivery DateTime?
  shippedAt   DateTime?
  deliveredAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("shipping")
}

// ================================
// 🎯 MARKETING & PROMOTIONS
// ================================

enum PromotionType {
  PERCENTAGE
  FIXED_AMOUNT
  BUY_X_GET_Y
  FREE_SHIPPING
}

enum CouponType {
  PERCENTAGE
  FIXED_AMOUNT
  FREE_SHIPPING
}

model Promotion {
  id          String        @id @default(cuid())
  name        String
  description String?
  type        PromotionType
  value       Decimal       @db.Decimal(10, 2)
  minAmount   Decimal?      @db.Decimal(10, 2)
  maxDiscount Decimal?      @db.Decimal(10, 2)
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean       @default(true)
  usageLimit  Int?
  usageCount  Int           @default(0)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  products ProductPromotion[]

  @@map("promotions")
}

model ProductPromotion {
  id          String @id @default(cuid())
  productId   String
  promotionId String

  // Relations
  product   Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
  promotion Promotion @relation(fields: [promotionId], references: [id], onDelete: Cascade)

  @@unique([productId, promotionId])
  @@map("product_promotions")
}

model Coupon {
  id          String     @id @default(cuid())
  code        String     @unique
  type        CouponType
  value       Decimal    @db.Decimal(10, 2)
  minAmount   Decimal?   @db.Decimal(10, 2)
  maxDiscount Decimal?   @db.Decimal(10, 2)
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean    @default(true)
  usageLimit  Int?
  usageCount  Int        @default(0)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  orders Order[]

  @@map("coupons")
}

model Newsletter {
  id          String   @id @default(cuid())
  email       String   @unique
  firstName   String?
  lastName    String?
  isActive    Boolean  @default(true)
  subscribedAt DateTime @default(now())
  unsubscribedAt DateTime?

  @@map("newsletters")
}

// ================================
// 💬 SUPPORT & REVIEWS
// ================================

enum TicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      // 1-5
  title     String?
  comment   String?
  isVerified Boolean @default(false)
  isApproved Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("reviews")
}

model SupportTicket {
  id          String         @id @default(cuid())
  userId      String
  subject     String
  description String
  status      TicketStatus   @default(OPEN)
  priority    TicketPriority @default(MEDIUM)
  assignedTo  String?
  orderId     String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]

  @@map("support_tickets")
}

model Message {
  id        String   @id @default(cuid())
  ticketId  String
  userId    String?
  content   String
  isInternal Boolean @default(false)
  createdAt DateTime @default(now())

  // Relations
  ticket SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  @@map("messages")
}

// ================================
// 📊 SYSTEM & AUDIT
// ================================

enum NotificationType {
  ORDER_CONFIRMATION
  ORDER_SHIPPED
  ORDER_DELIVERED
  STOCK_ALERT
  PROMOTION
  NEWSLETTER
}

model Notification {
  id        String           @id @default(cuid())
  userId    String?
  type      NotificationType
  title     String
  message   String
  data      Json?
  isRead    Boolean          @default(false)
  sentAt    DateTime?
  createdAt DateTime         @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  entity    String
  entityId  String
  oldData   Json?
  newData   Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

const { PrismaClient } = require('@prisma/client');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const slugify = require('slugify');
const uploadService = require('../services/uploadService');

const prisma = new PrismaClient();

class ProductController {
  // @desc    Get all products with filtering, sorting, and pagination
  // @route   GET /api/products
  // @access  Public
  async getProducts(req, res, next) {
    try {
      const {
        page = 1,
        limit = 12,
        category,
        search,
        minPrice,
        maxPrice,
        size,
        color,
        brand,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        featured
      } = req.query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

      // Build where clause
      const where = {
        status: 'ACTIVE',
        ...(category && { category: { slug: category } }),
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { tags: { has: search } }
          ]
        }),
        ...(brand && { brand: { contains: brand, mode: 'insensitive' } }),
        ...(featured && { isFeatured: featured === 'true' }),
        ...(minPrice || maxPrice) && {
          variants: {
            some: {
              price: {
                ...(minPrice && { gte: parseFloat(minPrice) }),
                ...(maxPrice && { lte: parseFloat(maxPrice) })
              }
            }
          }
        },
        ...(size || color) && {
          variants: {
            some: {
              ...(size && { size: { contains: size, mode: 'insensitive' } }),
              ...(color && { color: { contains: color, mode: 'insensitive' } })
            }
          }
        }
      };

      // Build orderBy clause
      const orderBy = {};
      if (sortBy === 'price') {
        orderBy.variants = { _count: 'desc' }; // This is a simplified approach
      } else {
        orderBy[sortBy] = sortOrder;
      }

      // Get products
      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            category: {
              select: { id: true, name: true, slug: true }
            },
            variants: {
              select: {
                id: true,
                sku: true,
                size: true,
                color: true,
                price: true,
                comparePrice: true,
                stock: true,
                isDefault: true
              },
              orderBy: { isDefault: 'desc' }
            },
            images: {
              select: { id: true, url: true, altText: true },
              orderBy: { sortOrder: 'asc' }
            },
            reviews: {
              select: { rating: true }
            }
          }
        }),
        prisma.product.count({ where })
      ]);

      // Calculate average rating for each product
      const productsWithRating = products.map(product => {
        const ratings = product.reviews.map(r => r.rating);
        const avgRating = ratings.length > 0 
          ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length 
          : 0;
        
        return {
          ...product,
          avgRating: Math.round(avgRating * 10) / 10,
          reviewCount: ratings.length,
          reviews: undefined // Remove reviews from response
        };
      });

      const totalPages = Math.ceil(total / take);

      res.json({
        status: 'success',
        data: {
          products: productsWithRating,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalItems: total,
            itemsPerPage: take,
            hasNextPage: parseInt(page) < totalPages,
            hasPrevPage: parseInt(page) > 1
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get single product by ID or slug
  // @route   GET /api/products/:identifier
  // @access  Public
  async getProduct(req, res, next) {
    try {
      const { identifier } = req.params;
      
      // Check if identifier is a valid UUID (ID) or slug
      const isId = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(identifier);
      
      const where = isId ? { id: identifier } : { slug: identifier };

      const product = await prisma.product.findUnique({
        where,
        include: {
          category: {
            select: { id: true, name: true, slug: true }
          },
          variants: {
            select: {
              id: true,
              sku: true,
              size: true,
              color: true,
              material: true,
              price: true,
              comparePrice: true,
              stock: true,
              weight: true,
              isDefault: true
            },
            orderBy: { isDefault: 'desc' }
          },
          images: {
            select: { id: true, url: true, altText: true },
            orderBy: { sortOrder: 'asc' }
          },
          reviews: {
            include: {
              user: {
                select: { firstName: true, lastName: true, avatar: true }
              }
            },
            where: { isApproved: true },
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      if (!product) {
        return next(new AppError('Produit non trouvé', 404, 'PRODUCT_NOT_FOUND'));
      }

      if (product.status !== 'ACTIVE') {
        return next(new AppError('Produit non disponible', 404, 'PRODUCT_UNAVAILABLE'));
      }

      // Calculate average rating
      const ratings = product.reviews.map(r => r.rating);
      const avgRating = ratings.length > 0 
        ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length 
        : 0;

      // Get related products
      const relatedProducts = await prisma.product.findMany({
        where: {
          categoryId: product.categoryId,
          id: { not: product.id },
          status: 'ACTIVE'
        },
        take: 4,
        include: {
          variants: {
            select: { price: true, stock: true },
            where: { isDefault: true }
          },
          images: {
            select: { url: true },
            take: 1,
            orderBy: { sortOrder: 'asc' }
          }
        }
      });

      const productWithRating = {
        ...product,
        avgRating: Math.round(avgRating * 10) / 10,
        reviewCount: ratings.length,
        relatedProducts
      };

      res.json({
        status: 'success',
        data: { product: productWithRating }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Create new product
  // @route   POST /api/products
  // @access  Private (Admin/Employee)
  async createProduct(req, res, next) {
    try {
      const {
        name,
        description,
        shortDesc,
        categoryId,
        brand,
        weight,
        dimensions,
        tags,
        seoTitle,
        seoDesc,
        variants,
        images
      } = req.body;

      // Generate slug
      const slug = slugify(name, { lower: true, strict: true });
      
      // Check if slug already exists
      const existingProduct = await prisma.product.findUnique({
        where: { slug }
      });

      if (existingProduct) {
        return next(new AppError('Un produit avec ce nom existe déjà', 409, 'PRODUCT_EXISTS'));
      }

      // Generate SKU
      const sku = `SF-${Date.now()}`;

      // Create product with variants and images
      const product = await prisma.product.create({
        data: {
          name,
          slug,
          description,
          shortDesc,
          sku,
          categoryId,
          brand,
          weight,
          dimensions,
          tags: tags || [],
          seoTitle,
          seoDesc,
          status: 'DRAFT',
          variants: {
            create: variants?.map((variant, index) => ({
              sku: `${sku}-${index + 1}`,
              size: variant.size,
              color: variant.color,
              material: variant.material,
              price: variant.price,
              comparePrice: variant.comparePrice,
              costPrice: variant.costPrice,
              stock: variant.stock || 0,
              weight: variant.weight,
              isDefault: index === 0 // First variant is default
            })) || []
          },
          images: {
            create: images?.map((image, index) => ({
              url: image.url,
              altText: image.altText || name,
              sortOrder: index
            })) || []
          }
        },
        include: {
          category: true,
          variants: true,
          images: true
        }
      });

      logger.logBusiness('Product created', {
        productId: product.id,
        name: product.name,
        userId: req.user.id
      });

      res.status(201).json({
        status: 'success',
        message: 'Produit créé avec succès',
        data: { product }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Update product
  // @route   PUT /api/products/:id
  // @access  Private (Admin/Employee)
  async updateProduct(req, res, next) {
    try {
      const { id } = req.params;
      const updateData = { ...req.body };

      // Remove variants and images from main update data
      delete updateData.variants;
      delete updateData.images;

      // Update slug if name changed
      if (updateData.name) {
        updateData.slug = slugify(updateData.name, { lower: true, strict: true });
      }

      const product = await prisma.product.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date()
        },
        include: {
          category: true,
          variants: true,
          images: true
        }
      });

      logger.logBusiness('Product updated', {
        productId: product.id,
        name: product.name,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: 'Produit mis à jour avec succès',
        data: { product }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Delete product
  // @route   DELETE /api/products/:id
  // @access  Private (Admin)
  async deleteProduct(req, res, next) {
    try {
      const { id } = req.params;

      // Check if product exists
      const product = await prisma.product.findUnique({
        where: { id },
        select: { id: true, name: true }
      });

      if (!product) {
        return next(new AppError('Produit non trouvé', 404, 'PRODUCT_NOT_FOUND'));
      }

      // Soft delete - change status to ARCHIVED
      await prisma.product.update({
        where: { id },
        data: { status: 'ARCHIVED' }
      });

      logger.logBusiness('Product deleted', {
        productId: product.id,
        name: product.name,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: 'Produit supprimé avec succès'
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Upload product images
  // @route   POST /api/products/:id/images
  // @access  Private (Admin/Employee)
  async uploadImages(req, res, next) {
    try {
      const { id } = req.params;
      const files = req.files;

      if (!files || files.length === 0) {
        return next(new AppError('Aucune image fournie', 400, 'NO_FILES'));
      }

      // Verify product exists
      const product = await prisma.product.findUnique({
        where: { id },
        select: { id: true, name: true }
      });

      if (!product) {
        return next(new AppError('Produit non trouvé', 404, 'PRODUCT_NOT_FOUND'));
      }

      // Upload images
      const uploadedImages = await uploadService.uploadImages(files, {
        folder: `sassoufashion/products/${id}`,
        tags: ['product', id]
      });

      // Get current image count for sort order
      const currentImageCount = await prisma.productImage.count({
        where: { productId: id }
      });

      // Save image records to database
      const imageRecords = await Promise.all(
        uploadedImages.map(async (uploadResult, index) => {
          return prisma.productImage.create({
            data: {
              productId: id,
              url: uploadResult.url,
              altText: `${product.name} - Image ${currentImageCount + index + 1}`,
              sortOrder: currentImageCount + index,
              publicId: uploadResult.publicId
            }
          });
        })
      );

      logger.logBusiness('Product images uploaded', {
        productId: id,
        imageCount: imageRecords.length,
        userId: req.user.id
      });

      res.status(201).json({
        status: 'success',
        message: 'Images uploadées avec succès',
        data: { images: imageRecords }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Delete product image
  // @route   DELETE /api/products/:id/images/:imageId
  // @access  Private (Admin/Employee)
  async deleteImage(req, res, next) {
    try {
      const { id, imageId } = req.params;

      // Find the image
      const image = await prisma.productImage.findFirst({
        where: {
          id: imageId,
          productId: id
        }
      });

      if (!image) {
        return next(new AppError('Image non trouvée', 404, 'IMAGE_NOT_FOUND'));
      }

      // Delete from storage
      await uploadService.deleteImage(image);

      // Delete from database
      await prisma.productImage.delete({
        where: { id: imageId }
      });

      logger.logBusiness('Product image deleted', {
        productId: id,
        imageId,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: 'Image supprimée avec succès'
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Update image order
  // @route   PUT /api/products/:id/images/reorder
  // @access  Private (Admin/Employee)
  async reorderImages(req, res, next) {
    try {
      const { id } = req.params;
      const { imageIds } = req.body;

      if (!Array.isArray(imageIds)) {
        return next(new AppError('Liste d\'IDs d\'images requise', 400, 'INVALID_IMAGE_IDS'));
      }

      // Update sort order for each image
      await Promise.all(
        imageIds.map((imageId, index) =>
          prisma.productImage.updateMany({
            where: {
              id: imageId,
              productId: id
            },
            data: { sortOrder: index }
          })
        )
      );

      logger.logBusiness('Product images reordered', {
        productId: id,
        imageCount: imageIds.length,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: 'Ordre des images mis à jour'
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get product analytics
  // @route   GET /api/products/:id/analytics
  // @access  Private (Admin/Employee)
  async getProductAnalytics(req, res, next) {
    try {
      const { id } = req.params;
      const { period = '30d' } = req.query;

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      // Get analytics data
      const [
        totalSales,
        totalRevenue,
        orderCount,
        avgRating,
        reviewCount,
        viewCount
      ] = await Promise.all([
        // Total units sold
        prisma.orderItem.aggregate({
          where: {
            variant: { productId: id },
            order: {
              status: { in: ['DELIVERED', 'CONFIRMED'] },
              createdAt: { gte: startDate, lte: endDate }
            }
          },
          _sum: { quantity: true }
        }),

        // Total revenue
        prisma.orderItem.aggregate({
          where: {
            variant: { productId: id },
            order: {
              status: { in: ['DELIVERED', 'CONFIRMED'] },
              createdAt: { gte: startDate, lte: endDate }
            }
          },
          _sum: { total: true }
        }),

        // Order count
        prisma.order.count({
          where: {
            items: {
              some: {
                variant: { productId: id }
              }
            },
            status: { in: ['DELIVERED', 'CONFIRMED'] },
            createdAt: { gte: startDate, lte: endDate }
          }
        }),

        // Average rating
        prisma.review.aggregate({
          where: {
            productId: id,
            isApproved: true,
            createdAt: { gte: startDate, lte: endDate }
          },
          _avg: { rating: true }
        }),

        // Review count
        prisma.review.count({
          where: {
            productId: id,
            isApproved: true,
            createdAt: { gte: startDate, lte: endDate }
          }
        }),

        // View count (if tracking is implemented)
        prisma.productView.count({
          where: {
            productId: id,
            createdAt: { gte: startDate, lte: endDate }
          }
        }).catch(() => 0) // Ignore if table doesn't exist
      ]);

      const analytics = {
        period,
        totalSales: totalSales._sum.quantity || 0,
        totalRevenue: totalRevenue._sum.total || 0,
        orderCount,
        avgRating: avgRating._avg.rating || 0,
        reviewCount,
        viewCount
      };

      res.json({
        status: 'success',
        data: { analytics }
      });

    } catch (error) {
      next(error);
    }
  }
}

module.exports = new ProductController();

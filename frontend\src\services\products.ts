import { apiClient, buildQueryString } from './api';
import type { 
  Product, 
  Category, 
  ProductFilters, 
  FilterOptions,
  ApiResponse, 
  PaginatedResponse 
} from '@/types';

// Product service
export const productService = {
  // Get all products with filters
  getProducts: async (filters: ProductFilters = {}): Promise<PaginatedResponse<Product>> => {
    const queryString = buildQueryString(filters);
    const url = queryString ? `/products?${queryString}` : '/products';
    
    const response = await apiClient.get<ApiResponse<PaginatedResponse<Product>>>(url);
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Invalid response format');
  },

  // Get single product by ID or slug
  getProduct: async (identifier: string): Promise<Product> => {
    const response = await apiClient.get<ApiResponse<{ product: Product }>>(`/products/${identifier}`);
    
    if (response.data?.product) {
      return response.data.product;
    }
    
    throw new Error('Product not found');
  },

  // Get featured products
  getFeaturedProducts: async (limit: number = 8): Promise<Product[]> => {
    const response = await productService.getProducts({ 
      featured: true, 
      limit,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
    
    return response.items;
  },

  // Get new arrivals
  getNewArrivals: async (limit: number = 8): Promise<Product[]> => {
    const response = await productService.getProducts({ 
      limit,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
    
    return response.items;
  },

  // Get best sellers (placeholder - would need sales data)
  getBestSellers: async (limit: number = 8): Promise<Product[]> => {
    const response = await productService.getProducts({ 
      limit,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
    
    return response.items;
  },

  // Search products
  searchProducts: async (query: string, filters: Omit<ProductFilters, 'search'> = {}): Promise<PaginatedResponse<Product>> => {
    return productService.getProducts({ 
      ...filters, 
      search: query 
    });
  },

  // Get search suggestions
  getSearchSuggestions: async (query: string): Promise<Product[]> => {
    if (query.length < 2) return [];
    
    const response = await apiClient.get<ApiResponse<{ suggestions: Product[] }>>(
      `/products/search/suggestions?q=${encodeURIComponent(query)}`
    );
    
    return response.data?.suggestions || [];
  },

  // Get filter options
  getFilterOptions: async (): Promise<FilterOptions> => {
    const response = await apiClient.get<ApiResponse<FilterOptions>>('/products/filters/options');
    
    if (response.data) {
      return response.data;
    }
    
    return {
      brands: [],
      sizes: [],
      colors: [],
      priceRange: { min: 0, max: 1000 }
    };
  },

  // Get products by category
  getProductsByCategory: async (categorySlug: string, filters: Omit<ProductFilters, 'category'> = {}): Promise<PaginatedResponse<Product>> => {
    return productService.getProducts({ 
      ...filters, 
      category: categorySlug 
    });
  },

  // Get related products
  getRelatedProducts: async (productId: string, limit: number = 4): Promise<Product[]> => {
    const product = await productService.getProduct(productId);
    
    // Get products from same category
    const response = await productService.getProductsByCategory(
      product.category.slug, 
      { limit: limit + 1 }
    );
    
    // Filter out the current product
    return response.items.filter(p => p.id !== productId).slice(0, limit);
  }
};

// Category service
export const categoryService = {
  // Get all categories
  getCategories: async (includeInactive: boolean = false): Promise<Category[]> => {
    const queryString = includeInactive ? '?includeInactive=true' : '';
    const response = await apiClient.get<ApiResponse<{ categories: Category[] }>>(`/categories${queryString}`);
    
    return response.data?.categories || [];
  },

  // Get single category
  getCategory: async (identifier: string): Promise<Category> => {
    const response = await apiClient.get<ApiResponse<{ category: Category }>>(`/categories/${identifier}`);
    
    if (response.data?.category) {
      return response.data.category;
    }
    
    throw new Error('Category not found');
  },

  // Get category products
  getCategoryProducts: async (
    identifier: string, 
    options: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      includeSubcategories?: boolean;
    } = {}
  ): Promise<{ category: Category; products: Product[]; pagination: any }> => {
    const queryString = buildQueryString(options);
    const url = queryString 
      ? `/categories/${identifier}/products?${queryString}`
      : `/categories/${identifier}/products`;
    
    const response = await apiClient.get<ApiResponse<{
      category: Category;
      products: Product[];
      pagination: any;
    }>>(url);
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Invalid response format');
  },

  // Get main categories (no parent)
  getMainCategories: async (): Promise<Category[]> => {
    const categories = await categoryService.getCategories();
    return categories.filter(cat => !cat.parentId);
  },

  // Get category hierarchy
  getCategoryHierarchy: async (): Promise<Category[]> => {
    const categories = await categoryService.getCategories();
    
    // Build hierarchy
    const categoryMap = new Map<string, Category>();
    const rootCategories: Category[] = [];
    
    // First pass: create map
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });
    
    // Second pass: build hierarchy
    categories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!;
      
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(categoryWithChildren);
        }
      } else {
        rootCategories.push(categoryWithChildren);
      }
    });
    
    return rootCategories;
  }
};

// Product utilities
export const productUtils = {
  // Get product price range
  getPriceRange: (product: Product): { min: number; max: number } => {
    if (!product.variants.length) return { min: 0, max: 0 };
    
    const prices = product.variants.map(v => v.price);
    return {
      min: Math.min(...prices),
      max: Math.max(...prices)
    };
  },

  // Get product availability
  isProductAvailable: (product: Product): boolean => {
    return product.status === 'ACTIVE' && 
           product.variants.some(v => v.stock > 0);
  },

  // Get product stock count
  getTotalStock: (product: Product): number => {
    return product.variants.reduce((total, variant) => total + variant.stock, 0);
  },

  // Get default variant
  getDefaultVariant: (product: Product) => {
    return product.variants.find(v => v.isDefault) || product.variants[0];
  },

  // Get product main image
  getMainImage: (product: Product): string => {
    return product.images[0]?.url || '/placeholder-product.jpg';
  },

  // Format product price
  formatPrice: (price: number, currency: string = 'TND'): string => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(price);
  },

  // Calculate discount percentage
  getDiscountPercentage: (price: number, comparePrice?: number): number => {
    if (!comparePrice || comparePrice <= price) return 0;
    return Math.round(((comparePrice - price) / comparePrice) * 100);
  },

  // Check if product is on sale
  isOnSale: (variant: any): boolean => {
    return variant.comparePrice && variant.comparePrice > variant.price;
  }
};

// Admin product service extensions
export const adminProductService = {
  // Create product
  createProduct: async (productData: any): Promise<Product> => {
    const response = await apiClient.post<ApiResponse<{ product: Product }>>('/products', productData);

    if (response.data?.product) {
      return response.data.product;
    }

    throw new Error('Failed to create product');
  },

  // Update product
  updateProduct: async (productId: string, productData: any): Promise<Product> => {
    const response = await apiClient.put<ApiResponse<{ product: Product }>>(`/products/${productId}`, productData);

    if (response.data?.product) {
      return response.data.product;
    }

    throw new Error('Failed to update product');
  },

  // Delete product
  deleteProduct: async (productId: string): Promise<void> => {
    await apiClient.delete<ApiResponse>(`/products/${productId}`);
  },

  // Upload product images
  uploadImages: async (productId: string, files: FileList): Promise<any[]> => {
    const formData = new FormData();

    Array.from(files).forEach((file) => {
      formData.append('images', file);
    });

    const response = await apiClient.post<ApiResponse<{ images: any[] }>>(
      `/products/${productId}/images/upload`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    if (response.data?.images) {
      return response.data.images;
    }

    throw new Error('Failed to upload images');
  },

  // Delete product image
  deleteImage: async (productId: string, imageId: string): Promise<void> => {
    await apiClient.delete<ApiResponse>(`/products/${productId}/images/${imageId}`);
  },

  // Reorder product images
  reorderImages: async (productId: string, imageIds: string[]): Promise<void> => {
    await apiClient.put<ApiResponse>(`/products/${productId}/images/reorder`, { imageIds });
  },

  // Get product analytics
  getProductAnalytics: async (productId: string, period: string = '30d'): Promise<any> => {
    const response = await apiClient.get<ApiResponse<{ analytics: any }>>(
      `/products/${productId}/analytics?period=${period}`
    );

    if (response.data?.analytics) {
      return response.data.analytics;
    }

    throw new Error('Failed to get analytics');
  }
};

export default productService;

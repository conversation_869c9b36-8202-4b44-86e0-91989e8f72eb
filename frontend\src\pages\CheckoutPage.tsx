import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '@/store/cartStore';
import { useAddresses } from '@/hooks/useUser';
import { useCheckout } from '@/hooks/useOrders';
import { paymentUtils } from '@/services/payments';
import { orderUtils } from '@/services/orders';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface CheckoutStep {
  id: string;
  title: string;
  completed: boolean;
}

const CheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const { items, subtotal, clearCart } = useCart();
  const { data: addresses } = useAddresses();
  const { checkout, isLoading } = useCheckout();

  const [currentStep, setCurrentStep] = useState(0);
  const [selectedAddress, setSelectedAddress] = useState<string>('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [shippingMethod, setShippingMethod] = useState<'standard' | 'express'>('standard');
  const [couponCode, setCouponCode] = useState('');
  const [loyaltyPointsUsed, setLoyaltyPointsUsed] = useState(0);
  const [notes, setNotes] = useState('');

  const steps: CheckoutStep[] = [
    { id: 'address', title: 'Adresse de livraison', completed: !!selectedAddress },
    { id: 'shipping', title: 'Mode de livraison', completed: !!shippingMethod },
    { id: 'payment', title: 'Paiement', completed: !!selectedPaymentMethod },
    { id: 'review', title: 'Confirmation', completed: false }
  ];

  // Redirect if cart is empty
  useEffect(() => {
    if (items.length === 0) {
      navigate('/cart');
    }
  }, [items, navigate]);

  // Auto-select default address
  useEffect(() => {
    if (addresses && addresses.length > 0 && !selectedAddress) {
      const defaultAddress = addresses.find(addr => addr.isDefault) || addresses[0];
      setSelectedAddress(defaultAddress.id);
    }
  }, [addresses, selectedAddress]);

  // Calculate totals
  const shippingCost = orderUtils.calculateShippingCost(subtotal, shippingMethod);
  const totals = orderUtils.calculateOrderTotals(items, shippingCost);

  const handleSubmitOrder = async () => {
    if (!selectedAddress || !selectedPaymentMethod) {
      return;
    }

    try {
      const orderData = {
        items: items.map(item => ({
          variantId: item.variantId,
          quantity: item.quantity
        })),
        addressId: selectedAddress,
        paymentMethod: selectedPaymentMethod as any,
        shippingMethod,
        notes: notes || undefined,
        couponCode: couponCode || undefined,
        loyaltyPointsUsed: loyaltyPointsUsed || undefined
      };

      const result = await checkout(orderData);

      // Clear cart on successful order
      clearCart();

      // Navigate to order confirmation
      navigate(`/orders/${result.order.id}`, {
        state: { orderCreated: true }
      });

    } catch (error) {
      console.error('Checkout error:', error);
    }
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8 bg-secondary-50">
      <div className="container-custom">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">Finaliser la commande</h1>

          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium
                    ${index <= currentStep
                      ? 'bg-primary-600 text-white'
                      : 'bg-secondary-200 text-secondary-600'
                    }
                  `}>
                    {index + 1}
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm font-medium ${
                      index <= currentStep ? 'text-primary-600' : 'text-secondary-600'
                    }`}>
                      {step.title}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`
                      w-16 h-0.5 mx-4
                      ${index < currentStep ? 'bg-primary-600' : 'bg-secondary-200'}
                    `} />
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Address Selection */}
              {currentStep >= 0 && (
                <div className="bg-white rounded-lg shadow-soft p-6">
                  <h2 className="text-xl font-semibold mb-4">Adresse de livraison</h2>
                  {addresses && addresses.length > 0 ? (
                    <div className="space-y-3">
                      {addresses.map((address) => (
                        <label key={address.id} className="flex items-start space-x-3 cursor-pointer">
                          <input
                            type="radio"
                            name="address"
                            value={address.id}
                            checked={selectedAddress === address.id}
                            onChange={(e) => setSelectedAddress(e.target.value)}
                            className="mt-1"
                          />
                          <div className="flex-1">
                            <div className="font-medium">
                              {address.firstName} {address.lastName}
                            </div>
                            <div className="text-secondary-600 text-sm">
                              {address.address1}
                              {address.address2 && `, ${address.address2}`}
                              <br />
                              {address.city}, {address.state} {address.postalCode}
                              {address.phone && <br />}
                              {address.phone}
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-secondary-600 mb-4">Aucune adresse enregistrée</p>
                      <button
                        onClick={() => navigate('/profile?tab=addresses')}
                        className="btn-primary"
                      >
                        Ajouter une adresse
                      </button>
                    </div>
                  )}

                  {selectedAddress && (
                    <button
                      onClick={() => setCurrentStep(Math.max(1, currentStep))}
                      className="btn-primary mt-4"
                    >
                      Continuer
                    </button>
                  )}
                </div>
              )}

              {/* Shipping Method */}
              {currentStep >= 1 && (
                <div className="bg-white rounded-lg shadow-soft p-6">
                  <h2 className="text-xl font-semibold mb-4">Mode de livraison</h2>
                  <div className="space-y-3">
                    <label className="flex items-center justify-between cursor-pointer p-3 border rounded-lg hover:bg-secondary-50">
                      <div className="flex items-center space-x-3">
                        <input
                          type="radio"
                          name="shipping"
                          value="standard"
                          checked={shippingMethod === 'standard'}
                          onChange={(e) => setShippingMethod(e.target.value as 'standard')}
                        />
                        <div>
                          <div className="font-medium">Livraison standard</div>
                          <div className="text-secondary-600 text-sm">3-5 jours ouvrables</div>
                        </div>
                      </div>
                      <div className="font-medium">
                        {subtotal >= 100 ? 'Gratuit' : '7 TND'}
                      </div>
                    </label>

                    <label className="flex items-center justify-between cursor-pointer p-3 border rounded-lg hover:bg-secondary-50">
                      <div className="flex items-center space-x-3">
                        <input
                          type="radio"
                          name="shipping"
                          value="express"
                          checked={shippingMethod === 'express'}
                          onChange={(e) => setShippingMethod(e.target.value as 'express')}
                        />
                        <div>
                          <div className="font-medium">Livraison express</div>
                          <div className="text-secondary-600 text-sm">1-2 jours ouvrables</div>
                        </div>
                      </div>
                      <div className="font-medium">15 TND</div>
                    </label>
                  </div>

                  <button
                    onClick={() => setCurrentStep(Math.max(2, currentStep))}
                    className="btn-primary mt-4"
                  >
                    Continuer
                  </button>
                </div>
              )}

              {/* Payment Method */}
              {currentStep >= 2 && (
                <div className="bg-white rounded-lg shadow-soft p-6">
                  <h2 className="text-xl font-semibold mb-4">Méthode de paiement</h2>
                  <div className="space-y-3">
                    {paymentUtils.getRecommendedPaymentMethods(totals.total).map((method) => (
                      <label key={method.method} className="flex items-center justify-between cursor-pointer p-3 border rounded-lg hover:bg-secondary-50">
                        <div className="flex items-center space-x-3">
                          <input
                            type="radio"
                            name="payment"
                            value={method.method}
                            checked={selectedPaymentMethod === method.method}
                            onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                          />
                          <div>
                            <div className="font-medium">{method.name}</div>
                            <div className="text-secondary-600 text-sm">{method.description}</div>
                          </div>
                        </div>
                        {method.fees > 0 && (
                          <div className="text-sm text-secondary-600">
                            +{method.fees} TND
                          </div>
                        )}
                      </label>
                    ))}
                  </div>

                  {selectedPaymentMethod && (
                    <button
                      onClick={() => setCurrentStep(Math.max(3, currentStep))}
                      className="btn-primary mt-4"
                    >
                      Continuer
                    </button>
                  )}
                </div>
              )}

              {/* Order Review */}
              {currentStep >= 3 && (
                <div className="bg-white rounded-lg shadow-soft p-6">
                  <h2 className="text-xl font-semibold mb-4">Confirmation de commande</h2>

                  {/* Order Notes */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Notes de commande (optionnel)
                    </label>
                    <textarea
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Instructions spéciales pour la livraison..."
                      className="input"
                      rows={3}
                    />
                  </div>

                  <button
                    onClick={handleSubmitOrder}
                    disabled={isLoading || !selectedAddress || !selectedPaymentMethod}
                    className="btn-primary w-full py-3 text-lg"
                  >
                    {isLoading ? (
                      <LoadingSpinner size="sm" color="white" />
                    ) : (
                      `Confirmer la commande - ${orderUtils.formatPrice(totals.total)}`
                    )}
                  </button>
                </div>
              )}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-soft p-6 sticky top-8">
                <h3 className="text-lg font-semibold mb-4">Résumé de commande</h3>

                {/* Items */}
                <div className="space-y-3 mb-4">
                  {items.map((item) => (
                    <div key={item.id} className="flex items-center space-x-3">
                      <img
                        src={item.product.images[0]?.url || '/placeholder-product.jpg'}
                        alt={item.product.name}
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-sm">{item.product.name}</div>
                        <div className="text-secondary-600 text-xs">
                          Qté: {item.quantity} × {orderUtils.formatPrice(item.variant.price)}
                        </div>
                      </div>
                      <div className="font-medium text-sm">
                        {orderUtils.formatPrice(item.variant.price * item.quantity)}
                      </div>
                    </div>
                  ))}
                </div>

                <hr className="my-4" />

                {/* Totals */}
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Sous-total</span>
                    <span>{orderUtils.formatPrice(totals.subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Livraison</span>
                    <span>{orderUtils.formatPrice(shippingCost)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>TVA (19%)</span>
                    <span>{orderUtils.formatPrice(totals.taxAmount)}</span>
                  </div>
                  <hr className="my-2" />
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total</span>
                    <span>{orderUtils.formatPrice(totals.total)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;

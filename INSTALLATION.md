# 🚀 Guide d'Installation - SassouFashion

## 📋 Prérequis

### Logiciels requis
- **Node.js** 18.0.0 ou supérieur
- **npm** 9.0.0 ou supérieur
- **PostgreSQL** 14.0 ou supérieur
- **Redis** (optionnel, pour le cache)

### Comptes de service (optionnels)
- **Cloudinary** (pour les images)
- **Stripe** (pour les paiements)
- **Twilio** (pour les SMS)
- **Gmail** ou autre SMTP (pour les emails)

## 🛠️ Installation

### 1. <PERSON><PERSON><PERSON> le projet
```bash
git clone https://github.com/sassoufashion/sassoufashion.git
cd sassoufashion
```

### 2. Configuration Backend

#### Installer les dépendances
```bash
cd backend
npm install
```

#### Configuration de l'environnement
```bash
# Copier le fichier d'exemple
cp .env.example .env

# Éditer le fichier .env avec vos configurations
nano .env
```

#### Variables d'environnement essentielles
```env
# Base de données
DATABASE_URL="postgresql://username:password@localhost:5432/sassoufashion"

# Sécurité
JWT_SECRET="votre-clé-jwt-super-secrète"

# Email (Gmail exemple)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=votre-mot-de-passe-app
```

#### Initialiser la base de données
```bash
# Générer le client Prisma
npx prisma generate

# Exécuter les migrations
npx prisma migrate dev

# (Optionnel) Peupler avec des données de test
npm run db:seed
```

#### Démarrer le serveur backend
```bash
npm run dev
```
Le serveur sera accessible sur `http://localhost:5000`

### 3. Configuration Frontend

#### Installer les dépendances
```bash
cd ../frontend
npm install
```

#### Configuration de l'environnement
```bash
# Créer le fichier d'environnement
echo "VITE_API_URL=http://localhost:5000/api" > .env.local
```

#### Démarrer le serveur frontend
```bash
npm run dev
```
L'application sera accessible sur `http://localhost:3000`

## 🔧 Configuration Avancée

### Base de données PostgreSQL

#### Installation sur Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### Création de la base de données
```bash
sudo -u postgres psql
CREATE DATABASE sassoufashion;
CREATE USER sassoufashion_user WITH PASSWORD 'votre_mot_de_passe';
GRANT ALL PRIVILEGES ON DATABASE sassoufashion TO sassoufashion_user;
\q
```

### Redis (optionnel)

#### Installation
```bash
# Ubuntu/Debian
sudo apt install redis-server

# macOS
brew install redis

# Démarrer Redis
redis-server
```

### Cloudinary (images)

1. Créer un compte sur [Cloudinary](https://cloudinary.com)
2. Récupérer vos clés API
3. Ajouter dans `.env`:
```env
CLOUDINARY_CLOUD_NAME=votre-cloud-name
CLOUDINARY_API_KEY=votre-api-key
CLOUDINARY_API_SECRET=votre-api-secret
```

### Stripe (paiements)

1. Créer un compte sur [Stripe](https://stripe.com)
2. Récupérer vos clés de test
3. Ajouter dans `.env`:
```env
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
```

## 🧪 Tests

### Backend
```bash
cd backend
npm test
npm run test:coverage
```

### Frontend
```bash
cd frontend
npm test
npm run test:coverage
```

## 📦 Production

### Build Frontend
```bash
cd frontend
npm run build
```

### Variables d'environnement de production
```env
NODE_ENV=production
DATABASE_URL="********************************/db"
JWT_SECRET="clé-production-très-sécurisée"
ALLOWED_ORIGINS="https://votre-domaine.com"
```

### Déploiement avec PM2
```bash
# Installer PM2
npm install -g pm2

# Démarrer l'application
cd backend
pm2 start src/server.js --name "sassoufashion-api"

# Servir le frontend avec nginx ou serveur statique
```

## 🐳 Docker (optionnel)

### Avec Docker Compose
```bash
# Démarrer tous les services
docker-compose up -d

# Voir les logs
docker-compose logs -f

# Arrêter les services
docker-compose down
```

## 🔍 Vérification de l'installation

### Backend
- API Health Check: `http://localhost:5000/health`
- Documentation API: `http://localhost:5000/api`
- Base de données: `npx prisma studio`

### Frontend
- Application: `http://localhost:3000`
- Build de production: `npm run build && npm run preview`

## 🆘 Dépannage

### Erreurs communes

#### "Port already in use"
```bash
# Trouver le processus utilisant le port
lsof -i :5000
# Tuer le processus
kill -9 PID
```

#### "Database connection failed"
- Vérifier que PostgreSQL est démarré
- Vérifier les credentials dans DATABASE_URL
- Tester la connexion: `psql "postgresql://user:pass@localhost:5432/db"`

#### "Module not found"
```bash
# Nettoyer et réinstaller
rm -rf node_modules package-lock.json
npm install
```

#### "Prisma client not generated"
```bash
npx prisma generate
```

## 📞 Support

- **Documentation**: [docs.sassoufashion.com](https://docs.sassoufashion.com)
- **Issues**: [GitHub Issues](https://github.com/sassoufashion/sassoufashion/issues)
- **Email**: <EMAIL>

## 🎉 Félicitations !

Votre installation de SassouFashion est maintenant prête ! 

Vous pouvez maintenant :
- ✅ Accéder à l'application sur `http://localhost:3000`
- ✅ Créer un compte administrateur
- ✅ Ajouter vos premiers produits
- ✅ Configurer les moyens de paiement
- ✅ Personnaliser le design

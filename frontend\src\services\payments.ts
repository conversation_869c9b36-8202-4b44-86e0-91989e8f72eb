import { apiClient } from './api';
import type { ApiResponse } from '@/types';

// Types for payment requests
export interface PaymentRequest {
  orderId: string;
  paymentMethod: 'CARD' | 'D17' | 'E_DINAR' | 'PAYPAL' | 'CASH_ON_DELIVERY';
  paymentData: any;
}

export interface StripePaymentData {
  paymentIntentId: string;
}

export interface D17PaymentData {
  phoneNumber: string;
}

export interface EDinarPaymentData {
  accountNumber: string;
  pin: string;
}

export interface PayPalPaymentData {
  paypalOrderId: string;
}

export interface PaymentResult {
  status: 'PAID' | 'PENDING' | 'FAILED';
  transactionId?: string;
  details?: any;
  error?: string;
}

export interface StripePaymentIntent {
  clientSecret: string;
  paymentIntentId: string;
}

// Payment service
export const paymentService = {
  // Process payment
  processPayment: async (paymentRequest: PaymentRequest): Promise<PaymentResult> => {
    const response = await apiClient.post<ApiResponse<{
      order: any;
      payment: PaymentResult;
    }>>('/payments/process', paymentRequest);
    
    if (response.data?.payment) {
      return response.data.payment;
    }
    
    throw new Error('Failed to process payment');
  },

  // Create Stripe payment intent
  createStripePaymentIntent: async (orderId: string): Promise<StripePaymentIntent> => {
    const response = await apiClient.post<ApiResponse<StripePaymentIntent>>(
      '/payments/stripe/intent',
      { orderId }
    );
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Failed to create payment intent');
  },

  // Process card payment with Stripe
  processCardPayment: async (orderId: string, paymentIntentId: string): Promise<PaymentResult> => {
    return paymentService.processPayment({
      orderId,
      paymentMethod: 'CARD',
      paymentData: { paymentIntentId }
    });
  },

  // Process D17 payment
  processD17Payment: async (orderId: string, phoneNumber: string): Promise<PaymentResult> => {
    return paymentService.processPayment({
      orderId,
      paymentMethod: 'D17',
      paymentData: { phoneNumber }
    });
  },

  // Process e-dinar payment
  processEDinarPayment: async (
    orderId: string, 
    accountNumber: string, 
    pin: string
  ): Promise<PaymentResult> => {
    return paymentService.processPayment({
      orderId,
      paymentMethod: 'E_DINAR',
      paymentData: { accountNumber, pin }
    });
  },

  // Process PayPal payment
  processPayPalPayment: async (orderId: string, paypalOrderId: string): Promise<PaymentResult> => {
    return paymentService.processPayment({
      orderId,
      paymentMethod: 'PAYPAL',
      paymentData: { paypalOrderId }
    });
  },

  // Process cash on delivery
  processCashOnDelivery: async (orderId: string): Promise<PaymentResult> => {
    return paymentService.processPayment({
      orderId,
      paymentMethod: 'CASH_ON_DELIVERY',
      paymentData: {}
    });
  }
};

// Payment utilities
export const paymentUtils = {
  // Get payment method info
  getPaymentMethodInfo: (method: string) => {
    const methodInfo = {
      CARD: {
        name: 'Carte bancaire',
        description: 'Visa, Mastercard, cartes internationales',
        icon: 'credit-card',
        fees: 0,
        processingTime: 'Immédiat'
      },
      D17: {
        name: 'D17',
        description: 'Paiement mobile D17',
        icon: 'smartphone',
        fees: 0,
        processingTime: 'Immédiat'
      },
      E_DINAR: {
        name: 'e-dinar',
        description: 'Portefeuille électronique e-dinar',
        icon: 'wallet',
        fees: 0,
        processingTime: 'Immédiat'
      },
      PAYPAL: {
        name: 'PayPal',
        description: 'Compte PayPal ou carte via PayPal',
        icon: 'paypal',
        fees: 0,
        processingTime: 'Immédiat'
      },
      CASH_ON_DELIVERY: {
        name: 'Paiement à la livraison',
        description: 'Payez en espèces lors de la réception',
        icon: 'banknote',
        fees: 5, // 5 TND fee for COD
        processingTime: 'À la livraison'
      }
    };

    return methodInfo[method as keyof typeof methodInfo] || {
      name: method,
      description: 'Méthode de paiement',
      icon: 'credit-card',
      fees: 0,
      processingTime: 'Inconnu'
    };
  },

  // Validate payment data
  validatePaymentData: (method: string, data: any) => {
    const errors: string[] = [];

    switch (method) {
      case 'CARD':
        if (!data.paymentIntentId) {
          errors.push('Données de carte manquantes');
        }
        break;

      case 'D17':
        if (!data.phoneNumber) {
          errors.push('Numéro de téléphone requis');
        } else if (!/^(\+216|216)?[2-9]\d{7}$/.test(data.phoneNumber.replace(/\s/g, ''))) {
          errors.push('Numéro de téléphone invalide');
        }
        break;

      case 'E_DINAR':
        if (!data.accountNumber) {
          errors.push('Numéro de compte e-dinar requis');
        }
        if (!data.pin) {
          errors.push('Code PIN requis');
        } else if (data.pin.length !== 4 || !/^\d{4}$/.test(data.pin)) {
          errors.push('Code PIN doit contenir 4 chiffres');
        }
        break;

      case 'PAYPAL':
        if (!data.paypalOrderId) {
          errors.push('ID de commande PayPal requis');
        }
        break;

      case 'CASH_ON_DELIVERY':
        // No validation needed for COD
        break;

      default:
        errors.push('Méthode de paiement non supportée');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Format phone number for D17
  formatD17PhoneNumber: (phoneNumber: string): string => {
    // Remove all non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');
    
    // Handle different formats
    if (digits.startsWith('216')) {
      return `+${digits}`;
    } else if (digits.length === 8) {
      return `+216${digits}`;
    } else if (digits.length === 11 && digits.startsWith('216')) {
      return `+${digits}`;
    }
    
    return phoneNumber;
  },

  // Calculate payment fees
  calculatePaymentFees: (method: string, amount: number): number => {
    const methodInfo = paymentUtils.getPaymentMethodInfo(method);
    return methodInfo.fees;
  },

  // Get payment status color
  getPaymentStatusColor: (status: string): string => {
    const colors = {
      PENDING: 'warning',
      PAID: 'success',
      FAILED: 'error',
      REFUNDED: 'secondary',
      PARTIALLY_REFUNDED: 'warning'
    };

    return colors[status as keyof typeof colors] || 'secondary';
  },

  // Check if payment method is available
  isPaymentMethodAvailable: (method: string, amount: number): boolean => {
    // Add business logic for payment method availability
    switch (method) {
      case 'CASH_ON_DELIVERY':
        // COD might have amount limits
        return amount <= 1000; // Max 1000 TND for COD
      
      case 'D17':
        // D17 might have daily limits
        return amount <= 500; // Max 500 TND per transaction
      
      case 'E_DINAR':
        // e-dinar might have limits
        return amount <= 2000; // Max 2000 TND per transaction
      
      default:
        return true;
    }
  },

  // Get payment method recommendations
  getRecommendedPaymentMethods: (amount: number, userLocation?: string) => {
    const methods = ['CARD', 'D17', 'E_DINAR', 'PAYPAL', 'CASH_ON_DELIVERY'];
    
    return methods
      .filter(method => paymentUtils.isPaymentMethodAvailable(method, amount))
      .map(method => ({
        method,
        ...paymentUtils.getPaymentMethodInfo(method),
        recommended: method === 'D17' || method === 'CARD' // Recommend popular methods
      }))
      .sort((a, b) => {
        // Sort by recommendation, then by fees
        if (a.recommended && !b.recommended) return -1;
        if (!a.recommended && b.recommended) return 1;
        return a.fees - b.fees;
      });
  },

  // Format payment amount
  formatAmount: (amount: number, currency: string = 'TND'): string => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  },

  // Generate payment reference
  generatePaymentReference: (orderId: string, method: string): string => {
    const timestamp = Date.now().toString(36);
    const methodCode = method.substring(0, 3).toUpperCase();
    return `${methodCode}_${orderId.substring(0, 8)}_${timestamp}`;
  }
};

export default paymentService;

const express = require('express');
const { body, param } = require('express-validator');
const { authenticate } = require('../middleware/auth');
const paymentController = require('../controllers/paymentController');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Validation middleware
const validatePaymentProcess = [
  body('orderId')
    .isUUID()
    .withMessage('ID de commande invalide'),
  body('paymentMethod')
    .isIn(['CARD', 'D17', 'E_DINAR', 'PAYPAL', 'CASH_ON_DELIVERY'])
    .withMessage('Méthode de paiement invalide'),
  body('paymentData')
    .isObject()
    .withMessage('Données de paiement requises')
];

const validateStripeIntent = [
  body('orderId')
    .isUUID()
    .withMessage('ID de commande invalide')
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    return next(new AppError(errorMessages.join(', '), 400, 'VALIDATION_ERROR'));
  }
  next();
};

// All routes require authentication
router.use(authenticate);

// Payment processing routes
router.post('/process', validatePaymentProcess, handleValidationErrors, paymentController.processPayment);
router.post('/stripe/intent', validateStripeIntent, handleValidationErrors, paymentController.createStripePaymentIntent);

// Webhook routes (no authentication required)
router.post('/webhooks/stripe', express.raw({ type: 'application/json' }), async (req, res, next) => {
  try {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!endpointSecret) {
      return res.status(400).send('Webhook secret not configured');
    }

    const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    let event;

    try {
      event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    } catch (err) {
      console.log(`Webhook signature verification failed.`, err.message);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        console.log('PaymentIntent was successful!', paymentIntent.id);
        // Update order status in database
        break;
      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object;
        console.log('PaymentIntent failed!', failedPayment.id);
        // Handle failed payment
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    next(error);
  }
});

// D17 webhook (mock)
router.post('/webhooks/d17', async (req, res, next) => {
  try {
    const { transactionId, status, orderId } = req.body;

    // Verify webhook signature (implement D17 signature verification)

    // Update payment status
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    await prisma.payment.updateMany({
      where: { transactionId },
      data: {
        status: status === 'SUCCESS' ? 'PAID' : 'FAILED',
        paidAt: status === 'SUCCESS' ? new Date() : null
      }
    });

    res.json({ status: 'success' });
  } catch (error) {
    next(error);
  }
});

// e-dinar webhook (mock)
router.post('/webhooks/edinar', async (req, res, next) => {
  try {
    const { transactionId, status, orderId } = req.body;

    // Update payment status
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    await prisma.payment.updateMany({
      where: { transactionId },
      data: {
        status: status === 'COMPLETED' ? 'PAID' : 'FAILED',
        paidAt: status === 'COMPLETED' ? new Date() : null
      }
    });

    res.json({ status: 'success' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;

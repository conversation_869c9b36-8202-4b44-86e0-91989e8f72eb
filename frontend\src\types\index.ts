// ================================
// 👤 USER TYPES
// ================================

export type UserRole = 'CLIENT' | 'EMPLOYEE' | 'ADMIN';
export type UserStatus = 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  role: UserRole;
  status: UserStatus;
  isVerified: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

// ================================
// 🛍️ PRODUCT TYPES
// ================================

export type ProductStatus = 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'ARCHIVED';

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: string;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  parent?: Category;
  children?: Category[];
  productCount?: number;
}

export interface ProductImage {
  id: string;
  url: string;
  altText?: string;
  sortOrder: number;
}

export interface ProductVariant {
  id: string;
  sku: string;
  size?: string;
  color?: string;
  material?: string;
  price: number;
  comparePrice?: number;
  costPrice?: number;
  stock: number;
  weight?: number;
  isDefault: boolean;
}

export interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  shortDesc?: string;
  sku: string;
  categoryId: string;
  brand?: string;
  status: ProductStatus;
  isFeatured: boolean;
  weight?: number;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
  tags: string[];
  seoTitle?: string;
  seoDesc?: string;
  createdAt: string;
  updatedAt: string;
  category: Category;
  variants: ProductVariant[];
  images: ProductImage[];
  avgRating: number;
  reviewCount: number;
  relatedProducts?: Product[];
}

// ================================
// 🧾 ORDER TYPES
// ================================

export type OrderStatus = 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED';
export type PaymentStatus = 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED' | 'PARTIALLY_REFUNDED';
export type PaymentMethod = 'CARD' | 'D17' | 'E_DINAR' | 'PAYPAL' | 'CASH_ON_DELIVERY';

export interface Address {
  id: string;
  type: string;
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
}

export interface OrderItem {
  id: string;
  variantId: string;
  quantity: number;
  price: number;
  total: number;
  variant: ProductVariant & {
    product: Pick<Product, 'id' | 'name' | 'slug' | 'images'>;
  };
}

export interface Payment {
  id: string;
  method: PaymentMethod;
  status: PaymentStatus;
  amount: number;
  currency: string;
  transactionId?: string;
  paidAt?: string;
}

export interface Shipping {
  id: string;
  method: string;
  carrier?: string;
  trackingNumber?: string;
  cost: number;
  estimatedDelivery?: string;
  shippedAt?: string;
  deliveredAt?: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  status: OrderStatus;
  subtotal: number;
  taxAmount: number;
  shippingCost: number;
  discountAmount: number;
  total: number;
  currency: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  items: OrderItem[];
  payment?: Payment;
  shipping?: Shipping;
  address: Address;
  user?: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
}

// ================================
// 🛒 CART TYPES
// ================================

export interface CartItem {
  id: string;
  productId: string;
  variantId: string;
  quantity: number;
  product: Product;
  variant: ProductVariant;
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  itemCount: number;
}

// ================================
// 💬 REVIEW TYPES
// ================================

export interface Review {
  id: string;
  userId: string;
  productId: string;
  rating: number;
  title?: string;
  comment?: string;
  isVerified: boolean;
  isApproved: boolean;
  createdAt: string;
  updatedAt: string;
  user: Pick<User, 'firstName' | 'lastName' | 'avatar'>;
}

// ================================
// 🎯 PROMOTION TYPES
// ================================

export type PromotionType = 'PERCENTAGE' | 'FIXED_AMOUNT' | 'BUY_X_GET_Y' | 'FREE_SHIPPING';
export type CouponType = 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SHIPPING';

export interface Promotion {
  id: string;
  name: string;
  description?: string;
  type: PromotionType;
  value: number;
  minAmount?: number;
  maxDiscount?: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  usageLimit?: number;
  usageCount: number;
}

export interface Coupon {
  id: string;
  code: string;
  type: CouponType;
  value: number;
  minAmount?: number;
  maxDiscount?: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  usageLimit?: number;
  usageCount: number;
}

// ================================
// 📊 API TYPES
// ================================

export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
  code?: string;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationInfo;
}

// ================================
// 🔍 SEARCH & FILTER TYPES
// ================================

export interface ProductFilters {
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  size?: string;
  color?: string;
  brand?: string;
  sortBy?: 'name' | 'price' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  featured?: boolean;
  page?: number;
  limit?: number;
}

export interface FilterOptions {
  brands: string[];
  sizes: string[];
  colors: string[];
  priceRange: {
    min: number;
    max: number;
  };
}

// ================================
// 🔔 NOTIFICATION TYPES
// ================================

export type NotificationType = 
  | 'ORDER_CONFIRMATION'
  | 'ORDER_SHIPPED'
  | 'ORDER_DELIVERED'
  | 'STOCK_ALERT'
  | 'PROMOTION'
  | 'NEWSLETTER';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
}

// ================================
// 📱 UI TYPES
// ================================

export interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

export interface Modal {
  id: string;
  component: React.ComponentType<any>;
  props?: any;
  options?: {
    closeOnOverlayClick?: boolean;
    closeOnEscape?: boolean;
    size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  };
}

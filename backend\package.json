{"name": "sassoufashion-backend", "version": "1.0.0", "description": "Backend API pour SassouFashion - Système de gestion e-commerce", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "build": "echo 'No build step required'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "node prisma/seed.js", "db:studio": "npx prisma studio", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["ecommerce", "fashion", "api", "nodejs", "express", "prisma", "postgresql"], "author": "SassouFashion Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "redis": "^4.6.11", "stripe": "^14.9.0", "twilio": "^4.19.0", "pdf-lib": "^1.17.1", "puppeteer": "^21.6.1", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1", "joi": "^17.11.0", "winston": "^3.11.0", "express-winston": "^4.2.0", "slugify": "^1.6.6"}, "devDependencies": {"prisma": "^5.7.0", "nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/sassoufashion/backend.git"}, "bugs": {"url": "https://github.com/sassoufashion/backend/issues"}, "homepage": "https://github.com/sassoufashion/backend#readme"}
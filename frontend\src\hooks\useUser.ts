import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { userService } from '@/services/users';
import type { 
  UpdateProfileRequest, 
  ChangePasswordRequest, 
  CreateAddressRequest 
} from '@/services/users';

// Query keys
export const userKeys = {
  all: ['user'] as const,
  profile: () => [...userKeys.all, 'profile'] as const,
  addresses: () => [...userKeys.all, 'addresses'] as const,
  loyaltyPoints: (page?: number) => [...userKeys.all, 'loyalty-points', page] as const,
  orders: (filters?: any) => [...userKeys.all, 'orders', filters] as const,
};

// Profile hooks
export const useProfile = () => {
  return useQuery({
    queryKey: userKeys.profile(),
    queryFn: userService.getProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateProfileRequest) => userService.updateProfile(data),
    onSuccess: (updatedUser) => {
      queryClient.setQueryData(userKeys.profile(), updatedUser);
      toast.success('Profil mis à jour avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la mise à jour du profil');
    },
  });
};

export const useChangePassword = () => {
  return useMutation({
    mutationFn: (data: ChangePasswordRequest) => userService.changePassword(data),
    onSuccess: () => {
      toast.success('Mot de passe modifié avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors du changement de mot de passe');
    },
  });
};

// Address hooks
export const useAddresses = () => {
  return useQuery({
    queryKey: userKeys.addresses(),
    queryFn: userService.getAddresses,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useAddAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAddressRequest) => userService.addAddress(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.addresses() });
      toast.success('Adresse ajoutée avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de l\'ajout de l\'adresse');
    },
  });
};

export const useUpdateAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      addressId, 
      data 
    }: { 
      addressId: string; 
      data: Partial<CreateAddressRequest> 
    }) => userService.updateAddress(addressId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.addresses() });
      toast.success('Adresse mise à jour avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la mise à jour de l\'adresse');
    },
  });
};

export const useDeleteAddress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (addressId: string) => userService.deleteAddress(addressId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.addresses() });
      toast.success('Adresse supprimée avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la suppression de l\'adresse');
    },
  });
};

// Loyalty points hooks
export const useLoyaltyPoints = (page: number = 1, limit: number = 20) => {
  return useQuery({
    queryKey: userKeys.loyaltyPoints(page),
    queryFn: () => userService.getLoyaltyPoints({ page, limit }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Orders hooks
export const useUserOrders = (filters: { 
  page?: number; 
  limit?: number; 
  status?: string; 
} = {}) => {
  return useQuery({
    queryKey: userKeys.orders(filters),
    queryFn: () => userService.getOrders(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Combined hooks for dashboard
export const useUserDashboard = () => {
  const profile = useProfile();
  const addresses = useAddresses();
  const loyaltyPoints = useLoyaltyPoints(1, 5);
  const recentOrders = useUserOrders({ page: 1, limit: 5 });

  return {
    profile,
    addresses,
    loyaltyPoints,
    recentOrders,
    isLoading: profile.isLoading || addresses.isLoading || loyaltyPoints.isLoading || recentOrders.isLoading,
    error: profile.error || addresses.error || loyaltyPoints.error || recentOrders.error,
  };
};

// Utility hooks
export const useDefaultAddress = () => {
  const { data: addresses } = useAddresses();
  return addresses?.find(address => address.isDefault) || addresses?.[0];
};

export const useLoyaltyBalance = () => {
  const { data: loyaltyData } = useLoyaltyPoints(1, 1);
  return loyaltyData?.summary?.balance || 0;
};

export const useUserStats = () => {
  const { data: profile } = useProfile();
  const { data: ordersData } = useUserOrders({ page: 1, limit: 1 });
  const loyaltyBalance = useLoyaltyBalance();

  return {
    totalOrders: ordersData?.pagination?.totalItems || 0,
    loyaltyPoints: loyaltyBalance,
    memberSince: profile?.createdAt ? new Date(profile.createdAt) : null,
    isVerified: profile?.isVerified || false,
  };
};

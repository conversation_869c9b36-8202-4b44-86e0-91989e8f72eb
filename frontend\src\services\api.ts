import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// Types
interface ApiConfig {
  baseURL: string;
  timeout: number;
}

interface ApiError {
  message: string;
  code?: string;
  status?: number;
}

// Configuration
const config: ApiConfig = {
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 30000, // 30 seconds
};

// Create axios instance
const api: AxiosInstance = axios.create(config);

// Token management
const TOKEN_KEY = 'sassoufashion_token';

export const tokenManager = {
  get: (): string | null => {
    return localStorage.getItem(TOKEN_KEY);
  },
  
  set: (token: string): void => {
    localStorage.setItem(TOKEN_KEY, token);
  },
  
  remove: (): void => {
    localStorage.removeItem(TOKEN_KEY);
  },
  
  isValid: (): boolean => {
    const token = tokenManager.get();
    if (!token) return false;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  }
};

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = tokenManager.get();
    
    if (token && tokenManager.isValid()) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() };
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log response time in development
    if (import.meta.env.DEV) {
      const endTime = new Date();
      const startTime = response.config.metadata?.startTime;
      if (startTime) {
        const duration = endTime.getTime() - startTime.getTime();
        console.log(`API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`);
      }
    }
    
    return response;
  },
  (error) => {
    const { response, request, message } = error;
    
    // Network error
    if (!response && request) {
      const networkError: ApiError = {
        message: 'Erreur de connexion. Vérifiez votre connexion internet.',
        code: 'NETWORK_ERROR',
        status: 0
      };
      
      toast.error(networkError.message);
      return Promise.reject(networkError);
    }
    
    // HTTP error response
    if (response) {
      const apiError: ApiError = {
        message: response.data?.message || 'Une erreur est survenue',
        code: response.data?.code || 'API_ERROR',
        status: response.status
      };
      
      // Handle specific error cases
      switch (response.status) {
        case 401:
          // Unauthorized - remove invalid token and redirect to login
          tokenManager.remove();
          if (window.location.pathname !== '/login') {
            toast.error('Session expirée. Veuillez vous reconnecter.');
            window.location.href = '/login';
          }
          break;
          
        case 403:
          toast.error('Accès non autorisé');
          break;
          
        case 404:
          // Don't show toast for 404 errors, let components handle them
          break;
          
        case 422:
          // Validation errors
          toast.error(apiError.message);
          break;
          
        case 429:
          toast.error('Trop de requêtes. Veuillez patienter.');
          break;
          
        case 500:
          toast.error('Erreur serveur. Veuillez réessayer plus tard.');
          break;
          
        default:
          if (response.status >= 400) {
            toast.error(apiError.message);
          }
      }
      
      return Promise.reject(apiError);
    }
    
    // Other errors
    const genericError: ApiError = {
      message: message || 'Une erreur inattendue est survenue',
      code: 'UNKNOWN_ERROR'
    };
    
    toast.error(genericError.message);
    return Promise.reject(genericError);
  }
);

// API methods
export const apiClient = {
  // GET request
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.get(url, config);
    return response.data;
  },
  
  // POST request
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.post(url, data, config);
    return response.data;
  },
  
  // PUT request
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.put(url, data, config);
    return response.data;
  },
  
  // PATCH request
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.patch(url, data, config);
    return response.data;
  },
  
  // DELETE request
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.delete(url, config);
    return response.data;
  }
};

// Utility functions
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, item.toString()));
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });
  
  return searchParams.toString();
};

export const createFormData = (data: Record<string, any>): FormData => {
  const formData = new FormData();
  
  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (value instanceof File) {
        formData.append(key, value);
      } else if (Array.isArray(value)) {
        value.forEach((item, index) => {
          if (item instanceof File) {
            formData.append(`${key}[${index}]`, item);
          } else {
            formData.append(`${key}[${index}]`, JSON.stringify(item));
          }
        });
      } else if (typeof value === 'object') {
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, value.toString());
      }
    }
  });
  
  return formData;
};

// Export the configured axios instance for direct use if needed
export { api };
export default apiClient;

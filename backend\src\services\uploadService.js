const cloudinary = require('cloudinary').v2;
const multer = require('multer');
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const path = require('path');
const fs = require('fs').promises;
const config = require('../config/config');
const logger = require('../utils/logger');

// Configure Cloudinary
cloudinary.config({
  cloud_name: config.cloudinary.cloudName,
  api_key: config.cloudinary.apiKey,
  api_secret: config.cloudinary.apiSecret
});

class UploadService {
  constructor() {
    this.setupMulter();
  }

  // Setup Multer for file uploads
  setupMulter() {
    // Cloudinary storage
    this.cloudinaryStorage = new CloudinaryStorage({
      cloudinary: cloudinary,
      params: {
        folder: 'sassoufashion',
        allowed_formats: ['jpg', 'jpeg', 'png', 'webp'],
        transformation: [
          { width: 1200, height: 1200, crop: 'limit', quality: 'auto' }
        ]
      }
    });

    // Local storage (fallback)
    this.localStorage = multer.diskStorage({
      destination: (req, file, cb) => {
        const uploadPath = path.join(__dirname, '../../uploads');
        cb(null, uploadPath);
      },
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
      }
    });

    // File filter
    this.fileFilter = (req, file, cb) => {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      
      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('Type de fichier non supporté. Utilisez JPG, PNG ou WebP.'), false);
      }
    };

    // Multer configuration
    this.upload = multer({
      storage: config.cloudinary.enabled ? this.cloudinaryStorage : this.localStorage,
      fileFilter: this.fileFilter,
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
        files: 10 // Max 10 files per upload
      }
    });
  }

  // Get multer middleware
  getUploadMiddleware() {
    return {
      single: (fieldName) => this.upload.single(fieldName),
      multiple: (fieldName, maxCount = 10) => this.upload.array(fieldName, maxCount),
      fields: (fields) => this.upload.fields(fields)
    };
  }

  // Upload single image
  async uploadImage(file, options = {}) {
    try {
      if (config.cloudinary.enabled) {
        return await this.uploadToCloudinary(file, options);
      } else {
        return await this.uploadToLocal(file, options);
      }
    } catch (error) {
      logger.error('Image upload failed', {
        filename: file.originalname,
        error: error.message
      });
      throw error;
    }
  }

  // Upload multiple images
  async uploadImages(files, options = {}) {
    try {
      const uploadPromises = files.map(file => this.uploadImage(file, options));
      return await Promise.all(uploadPromises);
    } catch (error) {
      logger.error('Multiple images upload failed', {
        fileCount: files.length,
        error: error.message
      });
      throw error;
    }
  }

  // Upload to Cloudinary
  async uploadToCloudinary(file, options = {}) {
    try {
      const result = await cloudinary.uploader.upload(file.path || file.buffer, {
        folder: options.folder || 'sassoufashion/products',
        public_id: options.publicId,
        transformation: options.transformation || [
          { width: 1200, height: 1200, crop: 'limit', quality: 'auto' }
        ],
        tags: options.tags || ['product']
      });

      return {
        url: result.secure_url,
        publicId: result.public_id,
        width: result.width,
        height: result.height,
        format: result.format,
        size: result.bytes
      };
    } catch (error) {
      logger.error('Cloudinary upload failed', {
        error: error.message
      });
      throw new Error('Échec de l\'upload sur Cloudinary');
    }
  }

  // Upload to local storage
  async uploadToLocal(file, options = {}) {
    try {
      const filename = file.filename || `${Date.now()}-${file.originalname}`;
      const uploadPath = path.join(__dirname, '../../uploads', filename);
      
      // Ensure upload directory exists
      await fs.mkdir(path.dirname(uploadPath), { recursive: true });
      
      // Move file if it's not already in the right place
      if (file.path && file.path !== uploadPath) {
        await fs.rename(file.path, uploadPath);
      }

      const baseUrl = config.app.url || 'http://localhost:5000';
      
      return {
        url: `${baseUrl}/uploads/${filename}`,
        filename: filename,
        path: uploadPath,
        size: file.size
      };
    } catch (error) {
      logger.error('Local upload failed', {
        error: error.message
      });
      throw new Error('Échec de l\'upload local');
    }
  }

  // Delete image
  async deleteImage(imageData) {
    try {
      if (config.cloudinary.enabled && imageData.publicId) {
        await cloudinary.uploader.destroy(imageData.publicId);
        logger.info('Image deleted from Cloudinary', {
          publicId: imageData.publicId
        });
      } else if (imageData.path) {
        await fs.unlink(imageData.path);
        logger.info('Image deleted from local storage', {
          path: imageData.path
        });
      }
    } catch (error) {
      logger.error('Image deletion failed', {
        imageData,
        error: error.message
      });
      // Don't throw error for deletion failures
    }
  }

  // Generate image transformations
  generateTransformations(baseUrl, transformations = []) {
    if (!config.cloudinary.enabled) {
      return { original: baseUrl };
    }

    const defaultTransformations = {
      thumbnail: 'w_150,h_150,c_fill,q_auto',
      small: 'w_300,h_300,c_limit,q_auto',
      medium: 'w_600,h_600,c_limit,q_auto',
      large: 'w_1200,h_1200,c_limit,q_auto'
    };

    const result = { original: baseUrl };

    Object.entries(defaultTransformations).forEach(([size, transformation]) => {
      result[size] = baseUrl.replace('/upload/', `/upload/${transformation}/`);
    });

    // Add custom transformations
    transformations.forEach(({ name, params }) => {
      result[name] = baseUrl.replace('/upload/', `/upload/${params}/`);
    });

    return result;
  }

  // Optimize image for web
  async optimizeImage(imageUrl, options = {}) {
    if (!config.cloudinary.enabled) {
      return imageUrl;
    }

    const optimizations = {
      quality: options.quality || 'auto',
      format: options.format || 'auto',
      width: options.width,
      height: options.height,
      crop: options.crop || 'limit'
    };

    const transformationString = Object.entries(optimizations)
      .filter(([key, value]) => value !== undefined)
      .map(([key, value]) => `${key.charAt(0)}_${value}`)
      .join(',');

    return imageUrl.replace('/upload/', `/upload/${transformationString}/`);
  }

  // Validate image
  validateImage(file) {
    const errors = [];

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      errors.push('Type de fichier non supporté. Utilisez JPG, PNG ou WebP.');
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      errors.push('Fichier trop volumineux. Taille maximum: 5MB.');
    }

    // Check filename
    if (!file.originalname || file.originalname.length > 255) {
      errors.push('Nom de fichier invalide.');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get image info
  async getImageInfo(imageUrl) {
    try {
      if (config.cloudinary.enabled && imageUrl.includes('cloudinary.com')) {
        const publicId = this.extractPublicIdFromUrl(imageUrl);
        const result = await cloudinary.api.resource(publicId);
        
        return {
          width: result.width,
          height: result.height,
          format: result.format,
          size: result.bytes,
          createdAt: result.created_at
        };
      }
      
      // For local images, return basic info
      return {
        url: imageUrl,
        local: true
      };
    } catch (error) {
      logger.error('Failed to get image info', {
        imageUrl,
        error: error.message
      });
      return null;
    }
  }

  // Extract public ID from Cloudinary URL
  extractPublicIdFromUrl(url) {
    const matches = url.match(/\/v\d+\/(.+)\./);
    return matches ? matches[1] : null;
  }

  // Cleanup old images
  async cleanupOldImages(olderThanDays = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      if (config.cloudinary.enabled) {
        // Cleanup Cloudinary images
        const result = await cloudinary.api.resources({
          type: 'upload',
          prefix: 'sassoufashion/',
          max_results: 500
        });

        const oldImages = result.resources.filter(resource => 
          new Date(resource.created_at) < cutoffDate
        );

        for (const image of oldImages) {
          await cloudinary.uploader.destroy(image.public_id);
        }

        logger.info('Cleaned up old Cloudinary images', {
          count: oldImages.length
        });
      } else {
        // Cleanup local images
        const uploadsDir = path.join(__dirname, '../../uploads');
        const files = await fs.readdir(uploadsDir);

        for (const file of files) {
          const filePath = path.join(uploadsDir, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime < cutoffDate) {
            await fs.unlink(filePath);
          }
        }

        logger.info('Cleaned up old local images');
      }
    } catch (error) {
      logger.error('Image cleanup failed', {
        error: error.message
      });
    }
  }
}

module.exports = new UploadService();

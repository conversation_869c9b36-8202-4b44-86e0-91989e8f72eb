import { apiClient, buildQueryString } from './api';
import type { 
  User, 
  Address, 
  Order,
  ApiResponse, 
  PaginatedResponse 
} from '@/types';

// Types for user requests
export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatar?: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface CreateAddressRequest {
  type?: string;
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country?: string;
  phone?: string;
  isDefault?: boolean;
}

export interface LoyaltyPoint {
  id: string;
  points: number;
  type: string;
  description: string;
  orderId?: string;
  expiresAt?: string;
  createdAt: string;
  order?: {
    orderNumber: string;
  };
}

export interface LoyaltyPointsResponse {
  points: LoyaltyPoint[];
  summary: {
    balance: number;
    earned: number;
    redeemed: number;
  };
  pagination: any;
}

// User service
export const userService = {
  // Get user profile
  getProfile: async (): Promise<User> => {
    const response = await apiClient.get<ApiResponse<{ user: User }>>('/users/profile');
    
    if (response.data?.user) {
      return response.data.user;
    }
    
    throw new Error('Failed to get profile');
  },

  // Update user profile
  updateProfile: async (profileData: UpdateProfileRequest): Promise<User> => {
    const response = await apiClient.put<ApiResponse<{ user: User }>>('/users/profile', profileData);
    
    if (response.data?.user) {
      return response.data.user;
    }
    
    throw new Error('Failed to update profile');
  },

  // Change password
  changePassword: async (passwords: ChangePasswordRequest): Promise<void> => {
    await apiClient.post<ApiResponse>('/users/change-password', passwords);
  },

  // Get user addresses
  getAddresses: async (): Promise<Address[]> => {
    const response = await apiClient.get<ApiResponse<{ addresses: Address[] }>>('/users/addresses');
    
    return response.data?.addresses || [];
  },

  // Add new address
  addAddress: async (addressData: CreateAddressRequest): Promise<Address> => {
    const response = await apiClient.post<ApiResponse<{ address: Address }>>('/users/addresses', addressData);
    
    if (response.data?.address) {
      return response.data.address;
    }
    
    throw new Error('Failed to add address');
  },

  // Update address
  updateAddress: async (addressId: string, addressData: Partial<CreateAddressRequest>): Promise<Address> => {
    const response = await apiClient.put<ApiResponse<{ address: Address }>>(`/users/addresses/${addressId}`, addressData);
    
    if (response.data?.address) {
      return response.data.address;
    }
    
    throw new Error('Failed to update address');
  },

  // Delete address
  deleteAddress: async (addressId: string): Promise<void> => {
    await apiClient.delete<ApiResponse>(`/users/addresses/${addressId}`);
  },

  // Get loyalty points
  getLoyaltyPoints: async (options: { page?: number; limit?: number } = {}): Promise<LoyaltyPointsResponse> => {
    const queryString = buildQueryString(options);
    const url = queryString ? `/users/loyalty-points?${queryString}` : '/users/loyalty-points';
    
    const response = await apiClient.get<ApiResponse<LoyaltyPointsResponse>>(url);
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Failed to get loyalty points');
  },

  // Get user orders
  getOrders: async (options: { 
    page?: number; 
    limit?: number; 
    status?: string; 
  } = {}): Promise<PaginatedResponse<Order>> => {
    const queryString = buildQueryString(options);
    const url = queryString ? `/users/orders?${queryString}` : '/users/orders';
    
    const response = await apiClient.get<ApiResponse<PaginatedResponse<Order>>>(url);
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Failed to get orders');
  }
};

// Address utilities
export const addressUtils = {
  // Format address for display
  formatAddress: (address: Address): string => {
    const parts = [
      address.address1,
      address.address2,
      address.city,
      address.state,
      address.postalCode
    ].filter(Boolean);
    
    return parts.join(', ');
  },

  // Get full name from address
  getFullName: (address: Address): string => {
    return `${address.firstName} ${address.lastName}`.trim();
  },

  // Validate address
  validateAddress: (address: Partial<CreateAddressRequest>) => {
    const errors: string[] = [];
    
    if (!address.firstName?.trim()) {
      errors.push('Le prénom est requis');
    }
    
    if (!address.lastName?.trim()) {
      errors.push('Le nom est requis');
    }
    
    if (!address.address1?.trim()) {
      errors.push('L\'adresse est requise');
    }
    
    if (!address.city?.trim()) {
      errors.push('La ville est requise');
    }
    
    if (!address.state?.trim()) {
      errors.push('La région est requise');
    }
    
    if (!address.postalCode?.trim()) {
      errors.push('Le code postal est requis');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

// Loyalty utilities
export const loyaltyUtils = {
  // Format points display
  formatPoints: (points: number): string => {
    return new Intl.NumberFormat('fr-FR').format(points);
  },

  // Calculate points value in currency
  calculatePointsValue: (points: number, pointValue: number = 0.01): number => {
    return points * pointValue;
  },

  // Format points value as currency
  formatPointsValue: (points: number, pointValue: number = 0.01, currency: string = 'TND'): string => {
    const value = loyaltyUtils.calculatePointsValue(points, pointValue);
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(value);
  },

  // Get points type color
  getPointsTypeColor: (type: string): string => {
    switch (type) {
      case 'earned':
        return 'text-success-600';
      case 'redeemed':
        return 'text-error-600';
      case 'expired':
        return 'text-secondary-400';
      default:
        return 'text-secondary-600';
    }
  },

  // Get points type icon
  getPointsTypeIcon: (type: string): string => {
    switch (type) {
      case 'earned':
        return 'plus';
      case 'redeemed':
        return 'minus';
      case 'expired':
        return 'clock';
      default:
        return 'circle';
    }
  },

  // Check if points are expiring soon (within 30 days)
  isExpiringSoon: (expiresAt?: string): boolean => {
    if (!expiresAt) return false;
    
    const expirationDate = new Date(expiresAt);
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    
    return expirationDate <= thirtyDaysFromNow;
  }
};

export default userService;

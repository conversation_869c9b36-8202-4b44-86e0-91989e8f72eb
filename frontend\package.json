{"name": "sassouf<PERSON>on-frontend", "version": "1.0.0", "description": "Frontend React.js pour SassouFashion - Boutique en ligne", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "zustand": "^4.4.7", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "date-fns": "^2.30.0", "socket.io-client": "^4.7.4", "react-intersection-observer": "^9.5.3", "swiper": "^11.0.5", "recharts": "^2.8.0", "react-select": "^5.8.0", "react-dropzone": "^14.2.3", "react-image-gallery": "^1.3.0", "react-loading-skeleton": "^3.3.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "typescript": "^5.2.2", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vitest": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "@vitest/ui": "^0.34.6", "@vitest/coverage-v8": "^0.34.6", "jsdom": "^23.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
const express = require('express');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// Placeholder routes - will be implemented later
router.get('/profile', authenticate, (req, res) => {
  res.json({ status: 'success', message: 'User profile endpoint' });
});

router.get('/', authenticate, authorize('ADMIN'), (req, res) => {
  res.json({ status: 'success', message: 'Users list endpoint' });
});

module.exports = router;

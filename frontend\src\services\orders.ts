import { apiClient, buildQueryString } from './api';
import type { 
  Order, 
  ApiResponse, 
  PaginatedResponse,
  CartItem 
} from '@/types';

// Types for order requests
export interface CreateOrderRequest {
  items: {
    variantId: string;
    quantity: number;
  }[];
  addressId: string;
  paymentMethod: 'CARD' | 'D17' | 'E_DINAR' | 'PAYPAL' | 'CASH_ON_DELIVERY';
  shippingMethod?: 'standard' | 'express';
  notes?: string;
  couponCode?: string;
  loyaltyPointsUsed?: number;
}

export interface OrderFilters {
  page?: number;
  limit?: number;
  status?: string;
  startDate?: string;
  endDate?: string;
}

// Order service
export const orderService = {
  // Create new order
  createOrder: async (orderData: CreateOrderRequest): Promise<Order> => {
    const response = await apiClient.post<ApiResponse<{ order: Order }>>('/orders', orderData);
    
    if (response.data?.order) {
      return response.data.order;
    }
    
    throw new Error('Failed to create order');
  },

  // Get user orders
  getOrders: async (filters: OrderFilters = {}): Promise<PaginatedResponse<Order>> => {
    const queryString = buildQueryString(filters);
    const url = queryString ? `/orders?${queryString}` : '/orders';
    
    const response = await apiClient.get<ApiResponse<PaginatedResponse<Order>>>(url);
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Failed to get orders');
  },

  // Get single order
  getOrder: async (orderId: string): Promise<Order> => {
    const response = await apiClient.get<ApiResponse<{ order: Order }>>(`/orders/${orderId}`);
    
    if (response.data?.order) {
      return response.data.order;
    }
    
    throw new Error('Order not found');
  },

  // Cancel order (if allowed)
  cancelOrder: async (orderId: string, reason?: string): Promise<void> => {
    await apiClient.patch<ApiResponse>(`/orders/${orderId}/cancel`, { reason });
  },

  // Request order refund
  requestRefund: async (orderId: string, reason: string, items?: string[]): Promise<void> => {
    await apiClient.post<ApiResponse>(`/orders/${orderId}/refund`, { reason, items });
  },

  // Track order
  trackOrder: async (orderNumber: string): Promise<Order> => {
    const response = await apiClient.get<ApiResponse<{ order: Order }>>(`/orders/track/${orderNumber}`);
    
    if (response.data?.order) {
      return response.data.order;
    }
    
    throw new Error('Order not found');
  }
};

// Order utilities
export const orderUtils = {
  // Calculate order totals from cart items
  calculateOrderTotals: (
    items: CartItem[], 
    shippingCost: number = 0, 
    taxRate: number = 0.19,
    discountAmount: number = 0
  ) => {
    const subtotal = items.reduce((total, item) => {
      return total + (item.variant.price * item.quantity);
    }, 0);

    const discountedSubtotal = subtotal - discountAmount;
    const taxAmount = discountedSubtotal * taxRate;
    const total = discountedSubtotal + taxAmount + shippingCost;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      shippingCost,
      total
    };
  },

  // Format order status for display
  getOrderStatusInfo: (status: string) => {
    const statusInfo = {
      PENDING: {
        label: 'En attente',
        color: 'warning',
        description: 'Votre commande est en attente de confirmation'
      },
      CONFIRMED: {
        label: 'Confirmée',
        color: 'success',
        description: 'Votre commande a été confirmée et sera bientôt préparée'
      },
      PROCESSING: {
        label: 'En préparation',
        color: 'primary',
        description: 'Votre commande est en cours de préparation'
      },
      SHIPPED: {
        label: 'Expédiée',
        color: 'primary',
        description: 'Votre commande a été expédiée'
      },
      DELIVERED: {
        label: 'Livrée',
        color: 'success',
        description: 'Votre commande a été livrée avec succès'
      },
      CANCELLED: {
        label: 'Annulée',
        color: 'error',
        description: 'Votre commande a été annulée'
      },
      REFUNDED: {
        label: 'Remboursée',
        color: 'secondary',
        description: 'Votre commande a été remboursée'
      }
    };

    return statusInfo[status as keyof typeof statusInfo] || {
      label: status,
      color: 'secondary',
      description: 'Statut inconnu'
    };
  },

  // Get payment status info
  getPaymentStatusInfo: (status: string) => {
    const statusInfo = {
      PENDING: {
        label: 'En attente',
        color: 'warning',
        description: 'Paiement en cours de traitement'
      },
      PAID: {
        label: 'Payé',
        color: 'success',
        description: 'Paiement confirmé'
      },
      FAILED: {
        label: 'Échec',
        color: 'error',
        description: 'Le paiement a échoué'
      },
      REFUNDED: {
        label: 'Remboursé',
        color: 'secondary',
        description: 'Paiement remboursé'
      },
      PARTIALLY_REFUNDED: {
        label: 'Partiellement remboursé',
        color: 'warning',
        description: 'Paiement partiellement remboursé'
      }
    };

    return statusInfo[status as keyof typeof statusInfo] || {
      label: status,
      color: 'secondary',
      description: 'Statut inconnu'
    };
  },

  // Get shipping status info
  getShippingStatusInfo: (status?: string) => {
    if (!status) {
      return {
        label: 'Non expédié',
        color: 'secondary',
        description: 'Commande pas encore expédiée'
      };
    }

    const statusInfo = {
      PENDING: {
        label: 'En préparation',
        color: 'warning',
        description: 'Expédition en préparation'
      },
      SHIPPED: {
        label: 'Expédié',
        color: 'primary',
        description: 'Colis expédié'
      },
      IN_TRANSIT: {
        label: 'En transit',
        color: 'primary',
        description: 'Colis en cours de livraison'
      },
      DELIVERED: {
        label: 'Livré',
        color: 'success',
        description: 'Colis livré avec succès'
      },
      FAILED: {
        label: 'Échec de livraison',
        color: 'error',
        description: 'Échec de la livraison'
      }
    };

    return statusInfo[status as keyof typeof statusInfo] || {
      label: status,
      color: 'secondary',
      description: 'Statut inconnu'
    };
  },

  // Check if order can be cancelled
  canCancelOrder: (order: Order): boolean => {
    const cancellableStatuses = ['PENDING', 'CONFIRMED'];
    return cancellableStatuses.includes(order.status);
  },

  // Check if order can be refunded
  canRequestRefund: (order: Order): boolean => {
    const refundableStatuses = ['DELIVERED'];
    const daysSinceDelivery = order.shipping?.deliveredAt 
      ? Math.floor((Date.now() - new Date(order.shipping.deliveredAt).getTime()) / (1000 * 60 * 60 * 24))
      : 0;
    
    return refundableStatuses.includes(order.status) && daysSinceDelivery <= 30; // 30 days return policy
  },

  // Format order number for display
  formatOrderNumber: (orderNumber: string): string => {
    // Format SF20241201001 as SF-2024-12-01-001
    if (orderNumber.length >= 13) {
      const prefix = orderNumber.substring(0, 2);
      const year = orderNumber.substring(2, 6);
      const month = orderNumber.substring(6, 8);
      const day = orderNumber.substring(8, 10);
      const sequence = orderNumber.substring(10);
      
      return `${prefix}-${year}-${month}-${day}-${sequence}`;
    }
    
    return orderNumber;
  },

  // Calculate estimated delivery date
  getEstimatedDelivery: (shippingMethod: string, orderDate: string): Date => {
    const orderDateTime = new Date(orderDate);
    const deliveryDays = shippingMethod === 'express' ? 1 : 3; // Express: 1 day, Standard: 3 days
    
    const estimatedDate = new Date(orderDateTime);
    estimatedDate.setDate(estimatedDate.getDate() + deliveryDays);
    
    return estimatedDate;
  },

  // Format price
  formatPrice: (price: number, currency: string = 'TND'): string => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(price);
  },

  // Validate order before creation
  validateOrder: (orderData: CreateOrderRequest) => {
    const errors: string[] = [];
    
    if (!orderData.items || orderData.items.length === 0) {
      errors.push('Aucun article dans la commande');
    }
    
    if (!orderData.addressId) {
      errors.push('Adresse de livraison requise');
    }
    
    if (!orderData.paymentMethod) {
      errors.push('Méthode de paiement requise');
    }
    
    orderData.items.forEach((item, index) => {
      if (!item.variantId) {
        errors.push(`Article ${index + 1}: Variante requise`);
      }
      
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Article ${index + 1}: Quantité invalide`);
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

export default orderService;

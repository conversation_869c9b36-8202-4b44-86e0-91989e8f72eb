import React from 'react';
import { Link } from 'react-router-dom';

const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-secondary-50">
      <div className="max-w-md mx-auto text-center px-4">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-primary-600">404</h1>
          <div className="text-2xl font-semibold text-secondary-900 mb-4">
            Page non trouvée
          </div>
          <p className="text-secondary-600 mb-8">
            D<PERSON><PERSON><PERSON>, la page que vous recherchez n'existe pas ou a été déplacée.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link
            to="/"
            className="btn-primary btn-lg w-full"
          >
            Retour à l'accueil
          </Link>
          
          <Link
            to="/products"
            className="btn-outline btn-lg w-full"
          >
            Voir nos produits
          </Link>
        </div>
        
        <div className="mt-8 text-sm text-secondary-500">
          Besoin d'aide ? <Link to="/contact" className="text-primary-600 hover:underline">Contactez-nous</Link>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;

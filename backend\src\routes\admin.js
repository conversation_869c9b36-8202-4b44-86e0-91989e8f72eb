const express = require('express');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// All admin routes require admin role
router.use(authenticate);
router.use(authorize('ADMIN'));

// Placeholder routes - will be implemented later
router.get('/dashboard', (req, res) => {
  res.json({ status: 'success', message: 'Admin dashboard endpoint' });
});

router.get('/stats', (req, res) => {
  res.json({ status: 'success', message: 'Admin stats endpoint' });
});

module.exports = router;

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { orderService, paymentService } from '@/services/orders';
import type { 
  CreateOrderRequest, 
  OrderFilters,
  PaymentRequest 
} from '@/services/orders';

// Query keys
export const orderKeys = {
  all: ['orders'] as const,
  lists: () => [...orderKeys.all, 'list'] as const,
  list: (filters: OrderFilters) => [...orderKeys.lists(), filters] as const,
  details: () => [...orderKeys.all, 'detail'] as const,
  detail: (id: string) => [...orderKeys.details(), id] as const,
  track: (orderNumber: string) => [...orderKeys.all, 'track', orderNumber] as const,
};

// Orders hooks
export const useOrders = (filters: OrderFilters = {}) => {
  return useQuery({
    queryKey: orderKeys.list(filters),
    queryFn: () => orderService.getOrders(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useOrder = (orderId: string) => {
  return useQuery({
    queryKey: orderKeys.detail(orderId),
    queryFn: () => orderService.getOrder(orderId),
    enabled: !!orderId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useTrackOrder = (orderNumber: string) => {
  return useQuery({
    queryKey: orderKeys.track(orderNumber),
    queryFn: () => orderService.trackOrder(orderNumber),
    enabled: !!orderNumber,
    staleTime: 30 * 1000, // 30 seconds
  });
};

// Order mutations
export const useCreateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (orderData: CreateOrderRequest) => orderService.createOrder(orderData),
    onSuccess: (order) => {
      // Invalidate orders list
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });
      
      // Add order to cache
      queryClient.setQueryData(orderKeys.detail(order.id), order);
      
      toast.success('Commande créée avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la création de la commande');
    },
  });
};

export const useCancelOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, reason }: { orderId: string; reason?: string }) => 
      orderService.cancelOrder(orderId, reason),
    onSuccess: (_, { orderId }) => {
      // Invalidate orders
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: orderKeys.detail(orderId) });
      
      toast.success('Commande annulée avec succès');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de l\'annulation de la commande');
    },
  });
};

export const useRequestRefund = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      orderId, 
      reason, 
      items 
    }: { 
      orderId: string; 
      reason: string; 
      items?: string[] 
    }) => orderService.requestRefund(orderId, reason, items),
    onSuccess: (_, { orderId }) => {
      // Invalidate orders
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: orderKeys.detail(orderId) });
      
      toast.success('Demande de remboursement envoyée');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de la demande de remboursement');
    },
  });
};

// Payment hooks
export const useProcessPayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (paymentRequest: PaymentRequest) => paymentService.processPayment(paymentRequest),
    onSuccess: (result, { orderId }) => {
      // Invalidate order data
      queryClient.invalidateQueries({ queryKey: orderKeys.detail(orderId) });
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });
      
      if (result.status === 'PAID') {
        toast.success('Paiement effectué avec succès');
      } else if (result.status === 'PENDING') {
        toast.success('Paiement en cours de traitement');
      } else {
        toast.error('Échec du paiement');
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors du paiement');
    },
  });
};

export const useCreateStripePaymentIntent = () => {
  return useMutation({
    mutationFn: (orderId: string) => paymentService.createStripePaymentIntent(orderId),
    onError: (error: any) => {
      toast.error(error.message || 'Erreur lors de l\'initialisation du paiement');
    },
  });
};

// Combined hooks for checkout flow
export const useCheckout = () => {
  const createOrder = useCreateOrder();
  const processPayment = useProcessPayment();
  const createStripeIntent = useCreateStripePaymentIntent();

  const checkout = async (orderData: CreateOrderRequest) => {
    try {
      // Step 1: Create order
      const order = await createOrder.mutateAsync(orderData);
      
      // Step 2: Process payment based on method
      let paymentResult;
      
      if (orderData.paymentMethod === 'CARD') {
        // For card payments, create Stripe payment intent first
        const intent = await createStripeIntent.mutateAsync(order.id);
        // Return intent for frontend to handle Stripe confirmation
        return { order, paymentIntent: intent };
      } else {
        // For other payment methods, process directly
        paymentResult = await processPayment.mutateAsync({
          orderId: order.id,
          paymentMethod: orderData.paymentMethod,
          paymentData: {} // Will be filled by specific payment components
        });
        
        return { order, paymentResult };
      }
    } catch (error) {
      throw error;
    }
  };

  return {
    checkout,
    isLoading: createOrder.isPending || processPayment.isPending || createStripeIntent.isPending,
    error: createOrder.error || processPayment.error || createStripeIntent.error
  };
};

// Utility hooks
export const useOrderStats = () => {
  const { data: ordersData } = useOrders({ limit: 1 });
  
  return {
    totalOrders: ordersData?.pagination?.totalItems || 0,
    isLoading: !ordersData
  };
};

export const useRecentOrders = (limit: number = 5) => {
  return useOrders({ 
    limit, 
    page: 1 
  });
};

export const useOrdersByStatus = (status: string) => {
  return useOrders({ 
    status, 
    limit: 10 
  });
};

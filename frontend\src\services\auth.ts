import { apiC<PERSON>, tokenManager } from './api';
import type { User, AuthResponse, ApiResponse } from '@/types';

// Types for auth requests
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatar?: string;
}

// Auth service
export const authService = {
  // Login user
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const response = await apiClient.post<ApiResponse<AuthResponse>>('/auth/login', credentials);
    
    if (response.data) {
      tokenManager.set(response.data.token);
      return response.data;
    }
    
    throw new Error('Invalid response format');
  },

  // Register new user
  register: async (userData: RegisterRequest): Promise<AuthResponse> => {
    const response = await apiClient.post<ApiResponse<AuthResponse>>('/auth/register', userData);
    
    if (response.data) {
      tokenManager.set(response.data.token);
      return response.data;
    }
    
    throw new Error('Invalid response format');
  },

  // Get current user
  getCurrentUser: async (): Promise<User> => {
    const response = await apiClient.get<ApiResponse<{ user: User }>>('/auth/me');
    
    if (response.data?.user) {
      return response.data.user;
    }
    
    throw new Error('User not found');
  },

  // Logout user
  logout: (): void => {
    tokenManager.remove();
    // Clear any cached data
    localStorage.removeItem('user');
    // Redirect to home page
    window.location.href = '/';
  },

  // Request password reset
  requestPasswordReset: async (email: string): Promise<void> => {
    await apiClient.post<ApiResponse>('/auth/forgot-password', { email });
  },

  // Reset password with token
  resetPassword: async (token: string, newPassword: string): Promise<void> => {
    await apiClient.post<ApiResponse>('/auth/reset-password', {
      token,
      password: newPassword
    });
  },

  // Change password (authenticated user)
  changePassword: async (passwords: ChangePasswordRequest): Promise<void> => {
    await apiClient.post<ApiResponse>('/auth/change-password', passwords);
  },

  // Update user profile
  updateProfile: async (profileData: UpdateProfileRequest): Promise<User> => {
    const response = await apiClient.put<ApiResponse<{ user: User }>>('/auth/profile', profileData);
    
    if (response.data?.user) {
      return response.data.user;
    }
    
    throw new Error('Failed to update profile');
  },

  // Verify email
  verifyEmail: async (token: string): Promise<void> => {
    await apiClient.post<ApiResponse>('/auth/verify-email', { token });
  },

  // Resend verification email
  resendVerification: async (): Promise<void> => {
    await apiClient.post<ApiResponse>('/auth/resend-verification');
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return tokenManager.isValid();
  },

  // Get stored token
  getToken: (): string | null => {
    return tokenManager.get();
  },

  // Refresh token (if implemented on backend)
  refreshToken: async (): Promise<string> => {
    const response = await apiClient.post<ApiResponse<{ token: string }>>('/auth/refresh');
    
    if (response.data?.token) {
      tokenManager.set(response.data.token);
      return response.data.token;
    }
    
    throw new Error('Failed to refresh token');
  }
};

// Auth utilities
export const authUtils = {
  // Get user role from token
  getUserRole: (): string | null => {
    const token = tokenManager.get();
    if (!token) return null;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.role || null;
    } catch {
      return null;
    }
  },

  // Check if user has specific role
  hasRole: (role: string): boolean => {
    const userRole = authUtils.getUserRole();
    return userRole === role;
  },

  // Check if user is admin
  isAdmin: (): boolean => {
    return authUtils.hasRole('ADMIN');
  },

  // Check if user is employee or admin
  isEmployee: (): boolean => {
    const role = authUtils.getUserRole();
    return role === 'EMPLOYEE' || role === 'ADMIN';
  },

  // Get user ID from token
  getUserId: (): string | null => {
    const token = tokenManager.get();
    if (!token) return null;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.id || null;
    } catch {
      return null;
    }
  },

  // Check if token is about to expire (within 5 minutes)
  isTokenExpiringSoon: (): boolean => {
    const token = tokenManager.get();
    if (!token) return false;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000;
      const currentTime = Date.now();
      const fiveMinutes = 5 * 60 * 1000;
      
      return expirationTime - currentTime < fiveMinutes;
    } catch {
      return false;
    }
  }
};

export default authService;

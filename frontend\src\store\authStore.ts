import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authService, authUtils } from '@/services/auth';
import type { User } from '@/types';

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
  }) => Promise<void>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Login action
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await authService.login({ email, password });
          
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.message || 'Erreur de connexion'
          });
          throw error;
        }
      },

      // Register action
      register: async (userData) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await authService.register(userData);
          
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.message || 'Erreur d\'inscription'
          });
          throw error;
        }
      },

      // Logout action
      logout: () => {
        authService.logout();
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        });
      },

      // Get current user
      getCurrentUser: async () => {
        try {
          // Check if token exists and is valid
          if (!authService.isAuthenticated()) {
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false
            });
            return;
          }

          set({ isLoading: true });
          
          const user = await authService.getCurrentUser();
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          // Token might be invalid, clear auth state
          authService.logout();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
        }
      },

      // Update profile
      updateProfile: async (data) => {
        try {
          set({ isLoading: true, error: null });
          
          const updatedUser = await authService.updateProfile(data);
          
          set({
            user: updatedUser,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || 'Erreur de mise à jour du profil'
          });
          throw error;
        }
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Set loading state
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      }),
      onRehydrateStorage: () => (state) => {
        // Verify token validity on rehydration
        if (state?.isAuthenticated && !authService.isAuthenticated()) {
          state.user = null;
          state.isAuthenticated = false;
        }
      }
    }
  )
);

// Auth selectors
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
    login: store.login,
    register: store.register,
    logout: store.logout,
    getCurrentUser: store.getCurrentUser,
    updateProfile: store.updateProfile,
    clearError: store.clearError
  };
};

// Role-based selectors
export const useAuthRole = () => {
  const user = useAuthStore(state => state.user);
  
  return {
    isAdmin: user?.role === 'ADMIN',
    isEmployee: user?.role === 'EMPLOYEE' || user?.role === 'ADMIN',
    isClient: user?.role === 'CLIENT',
    role: user?.role || null
  };
};

// Permission hooks
export const usePermissions = () => {
  const { isAdmin, isEmployee } = useAuthRole();
  
  return {
    canManageProducts: isEmployee,
    canManageOrders: isEmployee,
    canManageUsers: isAdmin,
    canViewAnalytics: isEmployee,
    canManageSettings: isAdmin
  };
};

export default useAuthStore;

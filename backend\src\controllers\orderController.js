const { PrismaClient } = require('@prisma/client');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const emailService = require('../services/emailService');
const loyaltyService = require('../services/loyaltyService');
const socketService = require('../services/socketService');

const prisma = new PrismaClient();

class OrderController {
  // @desc    Create new order
  // @route   POST /api/orders
  // @access  Private
  async createOrder(req, res, next) {
    try {
      const {
        items,
        addressId,
        paymentMethod,
        shippingMethod = 'standard',
        notes,
        couponCode,
        loyaltyPointsUsed = 0
      } = req.body;

      // Validate items
      if (!items || items.length === 0) {
        return next(new AppError('Aucun article dans la commande', 400, 'NO_ITEMS'));
      }

      // Validate address
      const address = await prisma.address.findFirst({
        where: { id: addressId, userId: req.user.id }
      });

      if (!address) {
        return next(new AppError('Adresse non trouvée', 404, 'ADDRESS_NOT_FOUND'));
      }

      // Validate and calculate order totals
      let subtotal = 0;
      const orderItems = [];

      for (const item of items) {
        const variant = await prisma.productVariant.findUnique({
          where: { id: item.variantId },
          include: {
            product: {
              select: { id: true, name: true, status: true }
            }
          }
        });

        if (!variant) {
          return next(new AppError(`Variante ${item.variantId} non trouvée`, 404, 'VARIANT_NOT_FOUND'));
        }

        if (variant.product.status !== 'ACTIVE') {
          return next(new AppError(`Produit ${variant.product.name} non disponible`, 400, 'PRODUCT_UNAVAILABLE'));
        }

        if (variant.stock < item.quantity) {
          return next(new AppError(`Stock insuffisant pour ${variant.product.name}`, 400, 'INSUFFICIENT_STOCK'));
        }

        const itemTotal = variant.price * item.quantity;
        subtotal += itemTotal;

        orderItems.push({
          variantId: variant.id,
          quantity: item.quantity,
          price: variant.price,
          total: itemTotal
        });
      }

      // Apply coupon if provided
      let discountAmount = 0;
      let coupon = null;

      if (couponCode) {
        coupon = await prisma.coupon.findFirst({
          where: {
            code: couponCode,
            isActive: true,
            startDate: { lte: new Date() },
            endDate: { gte: new Date() },
            OR: [
              { usageLimit: null },
              { usageCount: { lt: prisma.coupon.fields.usageLimit } }
            ]
          }
        });

        if (!coupon) {
          return next(new AppError('Code promo invalide ou expiré', 400, 'INVALID_COUPON'));
        }

        if (coupon.minAmount && subtotal < coupon.minAmount) {
          return next(new AppError(`Montant minimum ${coupon.minAmount} TND requis`, 400, 'MIN_AMOUNT_NOT_REACHED'));
        }

        // Calculate discount
        if (coupon.type === 'PERCENTAGE') {
          discountAmount = (subtotal * coupon.value) / 100;
          if (coupon.maxDiscount) {
            discountAmount = Math.min(discountAmount, coupon.maxDiscount);
          }
        } else if (coupon.type === 'FIXED_AMOUNT') {
          discountAmount = Math.min(coupon.value, subtotal);
        }
      }

      // Apply loyalty points
      let loyaltyDiscount = 0;
      if (loyaltyPointsUsed > 0) {
        const availablePoints = await loyaltyService.getAvailablePoints(req.user.id);
        
        if (loyaltyPointsUsed > availablePoints) {
          return next(new AppError('Points de fidélité insuffisants', 400, 'INSUFFICIENT_LOYALTY_POINTS'));
        }

        loyaltyDiscount = loyaltyService.calculateDiscount(loyaltyPointsUsed);
      }

      // Calculate shipping cost
      const shippingCost = this.calculateShippingCost(subtotal, shippingMethod, address);

      // Calculate tax (19% TVA in Tunisia)
      const taxRate = 0.19;
      const taxableAmount = subtotal - discountAmount - loyaltyDiscount;
      const taxAmount = taxableAmount * taxRate;

      // Calculate total
      const total = subtotal - discountAmount - loyaltyDiscount + shippingCost + taxAmount;

      // Generate order number
      const orderNumber = await this.generateOrderNumber();

      // Create order in transaction
      const order = await prisma.$transaction(async (tx) => {
        // Create order
        const newOrder = await tx.order.create({
          data: {
            orderNumber,
            userId: req.user.id,
            status: 'PENDING',
            subtotal,
            taxAmount,
            shippingCost,
            discountAmount: discountAmount + loyaltyDiscount,
            total,
            currency: 'TND',
            notes,
            addressId,
            items: {
              create: orderItems
            }
          },
          include: {
            items: {
              include: {
                variant: {
                  include: {
                    product: {
                      select: { id: true, name: true, slug: true, images: { take: 1 } }
                    }
                  }
                }
              }
            },
            address: true,
            user: {
              select: { id: true, firstName: true, lastName: true, email: true }
            }
          }
        });

        // Update stock
        for (const item of orderItems) {
          await tx.productVariant.update({
            where: { id: item.variantId },
            data: { stock: { decrement: item.quantity } }
          });

          // Log stock movement
          await tx.stockMovement.create({
            data: {
              productId: (await tx.productVariant.findUnique({
                where: { id: item.variantId },
                select: { productId: true }
              })).productId,
              variantId: item.variantId,
              type: 'out',
              quantity: item.quantity,
              reason: 'Order',
              reference: orderNumber
            }
          });
        }

        // Update coupon usage
        if (coupon) {
          await tx.coupon.update({
            where: { id: coupon.id },
            data: { usageCount: { increment: 1 } }
          });
        }

        // Redeem loyalty points
        if (loyaltyPointsUsed > 0) {
          await loyaltyService.redeemPoints(
            req.user.id,
            loyaltyPointsUsed,
            `Points utilisés pour la commande ${orderNumber}`
          );
        }

        return newOrder;
      });

      // Send order confirmation email
      try {
        await emailService.sendOrderConfirmationEmail(
          order.user.email,
          order,
          order.user.firstName
        );
      } catch (emailError) {
        logger.error('Failed to send order confirmation email', {
          orderId: order.id,
          error: emailError.message
        });
      }

      // Notify admins
      socketService.sendNewOrderAlert(order);

      logger.logBusiness('Order created', {
        orderId: order.id,
        orderNumber: order.orderNumber,
        userId: req.user.id,
        total: order.total,
        itemCount: order.items.length
      });

      res.status(201).json({
        status: 'success',
        message: 'Commande créée avec succès',
        data: { order }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get user orders
  // @route   GET /api/orders
  // @access  Private
  async getUserOrders(req, res, next) {
    try {
      const { page = 1, limit = 10, status } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

      const where = {
        userId: req.user.id,
        ...(status && { status })
      };

      const [orders, total] = await Promise.all([
        prisma.order.findMany({
          where,
          skip,
          take,
          orderBy: { createdAt: 'desc' },
          include: {
            items: {
              include: {
                variant: {
                  include: {
                    product: {
                      select: {
                        id: true,
                        name: true,
                        slug: true,
                        images: {
                          select: { url: true },
                          take: 1,
                          orderBy: { sortOrder: 'asc' }
                        }
                      }
                    }
                  }
                }
              }
            },
            payment: {
              select: {
                method: true,
                status: true,
                paidAt: true
              }
            },
            shipping: {
              select: {
                method: true,
                trackingNumber: true,
                estimatedDelivery: true,
                shippedAt: true,
                deliveredAt: true
              }
            }
          }
        }),
        prisma.order.count({ where })
      ]);

      const totalPages = Math.ceil(total / take);

      res.json({
        status: 'success',
        data: {
          orders,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalItems: total,
            itemsPerPage: take
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get single order
  // @route   GET /api/orders/:id
  // @access  Private
  async getOrder(req, res, next) {
    try {
      const { id } = req.params;

      const order = await prisma.order.findFirst({
        where: {
          id,
          userId: req.user.id
        },
        include: {
          items: {
            include: {
              variant: {
                include: {
                  product: {
                    select: {
                      id: true,
                      name: true,
                      slug: true,
                      images: {
                        select: { url: true, altText: true },
                        orderBy: { sortOrder: 'asc' }
                      }
                    }
                  }
                }
              }
            }
          },
          address: true,
          payment: true,
          shipping: true
        }
      });

      if (!order) {
        return next(new AppError('Commande non trouvée', 404, 'ORDER_NOT_FOUND'));
      }

      res.json({
        status: 'success',
        data: { order }
      });

    } catch (error) {
      next(error);
    }
  }

  // Helper methods
  calculateShippingCost(subtotal, method, address) {
    const freeShippingThreshold = 100; // 100 TND
    
    if (subtotal >= freeShippingThreshold) {
      return 0;
    }

    const shippingRates = {
      standard: 7, // 7 TND
      express: 15   // 15 TND
    };

    return shippingRates[method] || shippingRates.standard;
  }

  async generateOrderNumber() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    
    const prefix = `SF${year}${month}${day}`;
    
    // Get today's order count
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(startOfDay);
    endOfDay.setDate(endOfDay.getDate() + 1);
    
    const orderCount = await prisma.order.count({
      where: {
        createdAt: {
          gte: startOfDay,
          lt: endOfDay
        }
      }
    });
    
    const sequence = String(orderCount + 1).padStart(4, '0');
    return `${prefix}${sequence}`;
  }
}

module.exports = new OrderController();

import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth, useAuthRole } from '@/store/authStore';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'CLIENT' | 'EMPLOYEE' | 'ADMIN';
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  redirectTo = '/login'
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const { role } = useAuthRole();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <Navigate 
        to={redirectTo} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // Check role requirements
  if (requiredRole) {
    const hasRequiredRole = () => {
      switch (requiredRole) {
        case 'ADMIN':
          return role === 'ADMIN';
        case 'EMPLOYEE':
          return role === 'EMPLOYEE' || role === 'ADMIN';
        case 'CLIENT':
          return role === 'CLIENT' || role === 'EMPLOYEE' || role === 'ADMIN';
        default:
          return false;
      }
    };

    if (!hasRequiredRole()) {
      return (
        <Navigate 
          to="/unauthorized" 
          state={{ requiredRole, currentRole: role }} 
          replace 
        />
      );
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;

const express = require('express');
const { body, param, query } = require('express-validator');
const { authenticate, authorize, optionalAuth } = require('../middleware/auth');
const productController = require('../controllers/productController');
const variantController = require('../controllers/variantController');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Validation middleware
const validateProduct = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Le nom doit contenir entre 2 et 200 caractères'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('La description ne peut pas dépasser 2000 caractères'),
  body('categoryId')
    .isUUID()
    .withMessage('ID de catégorie invalide'),
  body('variants')
    .isArray({ min: 1 })
    .withMessage('Au moins une variante est requise'),
  body('variants.*.price')
    .isFloat({ min: 0 })
    .withMessage('Prix invalide'),
  body('variants.*.stock')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Stock invalide')
];

const validateProductUpdate = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Le nom doit contenir entre 2 et 200 caractères'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('La description ne peut pas dépasser 2000 caractères'),
  body('categoryId')
    .optional()
    .isUUID()
    .withMessage('ID de catégorie invalide')
];

const validateProductQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Numéro de page invalide'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite invalide (1-100)'),
  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Prix minimum invalide'),
  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Prix maximum invalide'),
  query('sortBy')
    .optional()
    .isIn(['name', 'price', 'createdAt', 'updatedAt'])
    .withMessage('Tri invalide'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Ordre de tri invalide')
];

const validateId = [
  param('id')
    .custom((value) => {
      // Accept both UUID and slug
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value);
      const isSlug = /^[a-z0-9-]+$/.test(value);
      
      if (!isUUID && !isSlug) {
        throw new Error('ID ou slug invalide');
      }
      return true;
    })
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    return next(new AppError(errorMessages.join(', '), 400, 'VALIDATION_ERROR'));
  }
  next();
};

// Public routes
router.get('/', 
  validateProductQuery, 
  handleValidationErrors, 
  optionalAuth,
  productController.getProducts
);

router.get('/:identifier', 
  validateId, 
  handleValidationErrors, 
  optionalAuth,
  productController.getProduct
);

// Protected routes (Admin/Employee)
router.post('/', 
  authenticate,
  authorize('ADMIN', 'EMPLOYEE'),
  validateProduct,
  handleValidationErrors,
  productController.createProduct
);

router.put('/:id', 
  authenticate,
  authorize('ADMIN', 'EMPLOYEE'),
  validateId,
  validateProductUpdate,
  handleValidationErrors,
  productController.updateProduct
);

router.delete('/:id', 
  authenticate,
  authorize('ADMIN'),
  validateId,
  handleValidationErrors,
  productController.deleteProduct
);

// Variant routes
router.get('/:id/variants', 
  validateId,
  handleValidationErrors,
  variantController.getProductVariants
);

router.post('/:id/variants', 
  authenticate,
  authorize('ADMIN', 'EMPLOYEE'),
  validateId,
  [
    body('size').optional().trim().isLength({ max: 50 }),
    body('color').optional().trim().isLength({ max: 50 }),
    body('material').optional().trim().isLength({ max: 100 }),
    body('price').isFloat({ min: 0 }).withMessage('Prix invalide'),
    body('stock').optional().isInt({ min: 0 }).withMessage('Stock invalide')
  ],
  handleValidationErrors,
  variantController.createVariant
);

router.put('/:id/variants/:variantId', 
  authenticate,
  authorize('ADMIN', 'EMPLOYEE'),
  validateId,
  [
    param('variantId').isUUID().withMessage('ID de variante invalide'),
    body('price').optional().isFloat({ min: 0 }).withMessage('Prix invalide'),
    body('stock').optional().isInt({ min: 0 }).withMessage('Stock invalide')
  ],
  handleValidationErrors,
  variantController.updateVariant
);

router.delete('/:id/variants/:variantId', 
  authenticate,
  authorize('ADMIN', 'EMPLOYEE'),
  validateId,
  [param('variantId').isUUID().withMessage('ID de variante invalide')],
  handleValidationErrors,
  variantController.deleteVariant
);

// Image routes
router.post('/:id/images', 
  authenticate,
  authorize('ADMIN', 'EMPLOYEE'),
  validateId,
  [
    body('images').isArray({ min: 1 }).withMessage('Au moins une image requise'),
    body('images.*.url').isURL().withMessage('URL d\'image invalide'),
    body('images.*.altText').optional().trim().isLength({ max: 200 })
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { id } = req.params;
      const { images } = req.body;

      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      // Add images to product
      const createdImages = await Promise.all(
        images.map((image, index) => 
          prisma.productImage.create({
            data: {
              productId: id,
              url: image.url,
              altText: image.altText || '',
              sortOrder: image.sortOrder || index
            }
          })
        )
      );

      res.status(201).json({
        status: 'success',
        message: 'Images ajoutées avec succès',
        data: { images: createdImages }
      });

    } catch (error) {
      next(error);
    }
  }
);

router.delete('/:id/images/:imageId', 
  authenticate,
  authorize('ADMIN', 'EMPLOYEE'),
  validateId,
  [param('imageId').isUUID().withMessage('ID d\'image invalide')],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { imageId } = req.params;

      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      await prisma.productImage.delete({
        where: { id: imageId }
      });

      res.json({
        status: 'success',
        message: 'Image supprimée avec succès'
      });

    } catch (error) {
      next(error);
    }
  }
);

// Search and filters
router.get('/search/suggestions', async (req, res, next) => {
  try {
    const { q } = req.query;
    
    if (!q || q.length < 2) {
      return res.json({
        status: 'success',
        data: { suggestions: [] }
      });
    }

    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    const suggestions = await prisma.product.findMany({
      where: {
        status: 'ACTIVE',
        OR: [
          { name: { contains: q, mode: 'insensitive' } },
          { tags: { has: q } }
        ]
      },
      select: {
        id: true,
        name: true,
        slug: true,
        images: {
          select: { url: true },
          take: 1,
          orderBy: { sortOrder: 'asc' }
        }
      },
      take: 5
    });

    res.json({
      status: 'success',
      data: { suggestions }
    });

  } catch (error) {
    next(error);
  }
});

router.get('/filters/options', async (req, res, next) => {
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    // Get unique filter options
    const [brands, sizes, colors, priceRange] = await Promise.all([
      prisma.product.findMany({
        where: { status: 'ACTIVE' },
        select: { brand: true },
        distinct: ['brand']
      }),
      prisma.productVariant.findMany({
        where: { product: { status: 'ACTIVE' } },
        select: { size: true },
        distinct: ['size']
      }),
      prisma.productVariant.findMany({
        where: { product: { status: 'ACTIVE' } },
        select: { color: true },
        distinct: ['color']
      }),
      prisma.productVariant.aggregate({
        where: { product: { status: 'ACTIVE' } },
        _min: { price: true },
        _max: { price: true }
      })
    ]);

    res.json({
      status: 'success',
      data: {
        brands: brands.map(b => b.brand).filter(Boolean),
        sizes: sizes.map(s => s.size).filter(Boolean),
        colors: colors.map(c => c.color).filter(Boolean),
        priceRange: {
          min: priceRange._min.price || 0,
          max: priceRange._max.price || 1000
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;

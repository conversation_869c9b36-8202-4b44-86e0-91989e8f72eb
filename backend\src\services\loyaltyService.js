const { PrismaClient } = require('@prisma/client');
const config = require('../config/config');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

class LoyaltyService {
  constructor() {
    this.pointsPerDinar = config.business.loyaltyPointsPerDinar || 1;
    this.pointValue = config.business.loyaltyPointsValue || 0.01;
  }

  // Calculate points earned from order amount
  calculatePointsEarned(orderAmount) {
    return Math.floor(orderAmount * this.pointsPerDinar);
  }

  // Calculate monetary value of points
  calculatePointsValue(points) {
    return points * this.pointValue;
  }

  // Award points for order
  async awardPointsForOrder(userId, orderId, orderAmount) {
    try {
      const pointsEarned = this.calculatePointsEarned(orderAmount);
      
      if (pointsEarned <= 0) return null;

      const loyaltyPoint = await prisma.loyaltyPoint.create({
        data: {
          userId,
          orderId,
          points: pointsEarned,
          type: 'earned',
          description: `Points gagnés pour la commande`,
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
        }
      });

      logger.logBusiness('Loyalty points awarded', {
        userId,
        orderId,
        pointsEarned,
        orderAmount
      });

      return loyaltyPoint;
    } catch (error) {
      logger.error('Failed to award loyalty points', {
        userId,
        orderId,
        orderAmount,
        error: error.message
      });
      throw error;
    }
  }

  // Redeem points for discount
  async redeemPoints(userId, pointsToRedeem, description = 'Points utilisés') {
    try {
      // Check available points
      const availablePoints = await this.getAvailablePoints(userId);
      
      if (pointsToRedeem > availablePoints) {
        throw new Error('Points insuffisants');
      }

      const loyaltyPoint = await prisma.loyaltyPoint.create({
        data: {
          userId,
          points: pointsToRedeem,
          type: 'redeemed',
          description
        }
      });

      logger.logBusiness('Loyalty points redeemed', {
        userId,
        pointsRedeemed: pointsToRedeem,
        description
      });

      return loyaltyPoint;
    } catch (error) {
      logger.error('Failed to redeem loyalty points', {
        userId,
        pointsToRedeem,
        error: error.message
      });
      throw error;
    }
  }

  // Get available points for user
  async getAvailablePoints(userId) {
    try {
      const result = await prisma.loyaltyPoint.aggregate({
        where: {
          userId,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        _sum: {
          points: true
        }
      });

      return result._sum.points || 0;
    } catch (error) {
      logger.error('Failed to get available points', {
        userId,
        error: error.message
      });
      return 0;
    }
  }

  // Get points breakdown (earned vs redeemed)
  async getPointsBreakdown(userId) {
    try {
      const breakdown = await prisma.loyaltyPoint.groupBy({
        by: ['type'],
        where: {
          userId,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        _sum: {
          points: true
        }
      });

      const earned = breakdown.find(b => b.type === 'earned')?._sum.points || 0;
      const redeemed = breakdown.find(b => b.type === 'redeemed')?._sum.points || 0;

      return {
        earned,
        redeemed,
        available: earned - redeemed
      };
    } catch (error) {
      logger.error('Failed to get points breakdown', {
        userId,
        error: error.message
      });
      return { earned: 0, redeemed: 0, available: 0 };
    }
  }

  // Get points history
  async getPointsHistory(userId, options = {}) {
    try {
      const { page = 1, limit = 20, type } = options;
      const skip = (page - 1) * limit;

      const where = {
        userId,
        ...(type && { type })
      };

      const [points, total] = await Promise.all([
        prisma.loyaltyPoint.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            order: {
              select: { orderNumber: true }
            }
          }
        }),
        prisma.loyaltyPoint.count({ where })
      ]);

      return {
        points,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: limit
        }
      };
    } catch (error) {
      logger.error('Failed to get points history', {
        userId,
        error: error.message
      });
      throw error;
    }
  }

  // Expire old points
  async expireOldPoints() {
    try {
      const expiredPoints = await prisma.loyaltyPoint.updateMany({
        where: {
          type: 'earned',
          expiresAt: { lt: new Date() }
        },
        data: {
          type: 'expired'
        }
      });

      logger.info('Expired old loyalty points', {
        count: expiredPoints.count
      });

      return expiredPoints.count;
    } catch (error) {
      logger.error('Failed to expire old points', {
        error: error.message
      });
      throw error;
    }
  }

  // Get user loyalty tier (based on total spending or points)
  async getUserLoyaltyTier(userId) {
    try {
      // Get total order amount for user
      const totalSpent = await prisma.order.aggregate({
        where: {
          userId,
          status: { in: ['DELIVERED', 'CONFIRMED'] }
        },
        _sum: {
          total: true
        }
      });

      const amount = totalSpent._sum.total || 0;

      // Define tiers
      const tiers = [
        { name: 'Bronze', minAmount: 0, benefits: ['Points de base'] },
        { name: 'Silver', minAmount: 500, benefits: ['Points de base', 'Livraison gratuite'] },
        { name: 'Gold', minAmount: 1500, benefits: ['Points de base', 'Livraison gratuite', 'Accès prioritaire'] },
        { name: 'Platinum', minAmount: 3000, benefits: ['Points de base', 'Livraison gratuite', 'Accès prioritaire', 'Remises exclusives'] }
      ];

      // Find appropriate tier
      let userTier = tiers[0];
      for (const tier of tiers) {
        if (amount >= tier.minAmount) {
          userTier = tier;
        }
      }

      return {
        ...userTier,
        totalSpent: amount,
        nextTier: tiers.find(t => t.minAmount > amount) || null
      };
    } catch (error) {
      logger.error('Failed to get user loyalty tier', {
        userId,
        error: error.message
      });
      return {
        name: 'Bronze',
        minAmount: 0,
        benefits: ['Points de base'],
        totalSpent: 0,
        nextTier: null
      };
    }
  }

  // Calculate discount from points
  calculateDiscount(points) {
    return this.calculatePointsValue(points);
  }

  // Validate points redemption
  async validateRedemption(userId, pointsToRedeem) {
    const availablePoints = await this.getAvailablePoints(userId);
    
    if (pointsToRedeem <= 0) {
      return { valid: false, error: 'Nombre de points invalide' };
    }

    if (pointsToRedeem > availablePoints) {
      return { 
        valid: false, 
        error: `Points insuffisants. Disponible: ${availablePoints}` 
      };
    }

    const discountValue = this.calculateDiscount(pointsToRedeem);
    
    return {
      valid: true,
      pointsToRedeem,
      discountValue,
      remainingPoints: availablePoints - pointsToRedeem
    };
  }

  // Get loyalty statistics for admin
  async getLoyaltyStatistics() {
    try {
      const [totalPoints, activeUsers, pointsBreakdown] = await Promise.all([
        prisma.loyaltyPoint.aggregate({
          _sum: { points: true }
        }),
        prisma.user.count({
          where: {
            loyaltyPoints: {
              some: {}
            }
          }
        }),
        prisma.loyaltyPoint.groupBy({
          by: ['type'],
          _sum: { points: true },
          _count: true
        })
      ]);

      return {
        totalPoints: totalPoints._sum.points || 0,
        activeUsers,
        breakdown: pointsBreakdown.reduce((acc, item) => {
          acc[item.type] = {
            points: item._sum.points || 0,
            transactions: item._count
          };
          return acc;
        }, {})
      };
    } catch (error) {
      logger.error('Failed to get loyalty statistics', {
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = new LoyaltyService();

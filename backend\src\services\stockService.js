const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const socketService = require('./socketService');

const prisma = new PrismaClient();

class StockService {
  constructor() {
    this.lowStockThreshold = 5;
    this.outOfStockThreshold = 0;
  }

  // Update stock for a variant
  async updateStock(variantId, quantity, type = 'adjustment', reason = '', reference = '') {
    try {
      const variant = await prisma.productVariant.findUnique({
        where: { id: variantId },
        include: {
          product: {
            select: { id: true, name: true }
          }
        }
      });

      if (!variant) {
        throw new Error('Variante de produit non trouvée');
      }

      const oldStock = variant.stock;
      let newStock;

      switch (type) {
        case 'in':
          newStock = oldStock + quantity;
          break;
        case 'out':
          newStock = Math.max(0, oldStock - quantity);
          break;
        case 'adjustment':
          newStock = quantity;
          break;
        default:
          throw new Error('Type de mouvement de stock invalide');
      }

      // Update variant stock
      const updatedVariant = await prisma.productVariant.update({
        where: { id: variantId },
        data: { stock: newStock }
      });

      // Log stock movement
      await prisma.stockMovement.create({
        data: {
          productId: variant.productId,
          variantId,
          type,
          quantity: type === 'adjustment' ? newStock - oldStock : quantity,
          previousStock: oldStock,
          newStock,
          reason,
          reference
        }
      });

      // Check for low stock alerts
      await this.checkStockAlerts(variant.product, updatedVariant);

      logger.logBusiness('Stock updated', {
        variantId,
        productName: variant.product.name,
        oldStock,
        newStock,
        type,
        quantity
      });

      return updatedVariant;
    } catch (error) {
      logger.error('Failed to update stock', {
        variantId,
        quantity,
        type,
        error: error.message
      });
      throw error;
    }
  }

  // Bulk update stock for multiple variants
  async bulkUpdateStock(updates) {
    try {
      const results = [];

      for (const update of updates) {
        const { variantId, quantity, type, reason, reference } = update;
        const result = await this.updateStock(variantId, quantity, type, reason, reference);
        results.push(result);
      }

      logger.logBusiness('Bulk stock update completed', {
        updateCount: updates.length
      });

      return results;
    } catch (error) {
      logger.error('Failed to bulk update stock', {
        updateCount: updates.length,
        error: error.message
      });
      throw error;
    }
  }

  // Reserve stock for order
  async reserveStock(orderItems) {
    try {
      const reservations = [];

      for (const item of orderItems) {
        const { variantId, quantity } = item;

        const variant = await prisma.productVariant.findUnique({
          where: { id: variantId },
          include: {
            product: {
              select: { id: true, name: true }
            }
          }
        });

        if (!variant) {
          throw new Error(`Variante ${variantId} non trouvée`);
        }

        if (variant.stock < quantity) {
          throw new Error(`Stock insuffisant pour ${variant.product.name}`);
        }

        // Create stock reservation
        const reservation = await prisma.stockReservation.create({
          data: {
            variantId,
            quantity,
            expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
          }
        });

        // Update available stock
        await prisma.productVariant.update({
          where: { id: variantId },
          data: { stock: variant.stock - quantity }
        });

        reservations.push(reservation);

        logger.info('Stock reserved', {
          variantId,
          quantity,
          reservationId: reservation.id
        });
      }

      return reservations;
    } catch (error) {
      logger.error('Failed to reserve stock', {
        error: error.message
      });
      throw error;
    }
  }

  // Release stock reservation
  async releaseReservation(reservationId) {
    try {
      const reservation = await prisma.stockReservation.findUnique({
        where: { id: reservationId },
        include: {
          variant: {
            include: {
              product: {
                select: { name: true }
              }
            }
          }
        }
      });

      if (!reservation) {
        throw new Error('Réservation non trouvée');
      }

      // Restore stock
      await prisma.productVariant.update({
        where: { id: reservation.variantId },
        data: {
          stock: {
            increment: reservation.quantity
          }
        }
      });

      // Delete reservation
      await prisma.stockReservation.delete({
        where: { id: reservationId }
      });

      logger.info('Stock reservation released', {
        reservationId,
        variantId: reservation.variantId,
        quantity: reservation.quantity
      });

      return true;
    } catch (error) {
      logger.error('Failed to release stock reservation', {
        reservationId,
        error: error.message
      });
      throw error;
    }
  }

  // Clean up expired reservations
  async cleanupExpiredReservations() {
    try {
      const expiredReservations = await prisma.stockReservation.findMany({
        where: {
          expiresAt: { lt: new Date() }
        },
        include: {
          variant: true
        }
      });

      for (const reservation of expiredReservations) {
        // Restore stock
        await prisma.productVariant.update({
          where: { id: reservation.variantId },
          data: {
            stock: {
              increment: reservation.quantity
            }
          }
        });
      }

      // Delete expired reservations
      const deletedCount = await prisma.stockReservation.deleteMany({
        where: {
          expiresAt: { lt: new Date() }
        }
      });

      logger.info('Expired stock reservations cleaned up', {
        count: deletedCount.count
      });

      return deletedCount.count;
    } catch (error) {
      logger.error('Failed to cleanup expired reservations', {
        error: error.message
      });
      throw error;
    }
  }

  // Check stock alerts
  async checkStockAlerts(product, variant) {
    try {
      if (variant.stock <= this.outOfStockThreshold) {
        // Out of stock alert
        socketService.sendStockAlert(product, variant, variant.stock);
        
        logger.logBusiness('Out of stock alert', {
          productId: product.id,
          variantId: variant.id,
          stock: variant.stock
        });
      } else if (variant.stock <= this.lowStockThreshold) {
        // Low stock alert
        socketService.sendStockAlert(product, variant, variant.stock);
        
        logger.logBusiness('Low stock alert', {
          productId: product.id,
          variantId: variant.id,
          stock: variant.stock
        });
      }
    } catch (error) {
      logger.error('Failed to check stock alerts', {
        productId: product.id,
        variantId: variant.id,
        error: error.message
      });
    }
  }

  // Get low stock products
  async getLowStockProducts(threshold = null) {
    try {
      const stockThreshold = threshold || this.lowStockThreshold;

      const lowStockVariants = await prisma.productVariant.findMany({
        where: {
          stock: { lte: stockThreshold },
          product: { status: 'ACTIVE' }
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
              images: {
                take: 1,
                orderBy: { sortOrder: 'asc' }
              }
            }
          }
        },
        orderBy: { stock: 'asc' }
      });

      return lowStockVariants;
    } catch (error) {
      logger.error('Failed to get low stock products', {
        error: error.message
      });
      throw error;
    }
  }

  // Get stock movements history
  async getStockMovements(filters = {}) {
    try {
      const {
        productId,
        variantId,
        type,
        startDate,
        endDate,
        page = 1,
        limit = 50
      } = filters;

      const skip = (page - 1) * limit;

      const where = {
        ...(productId && { productId }),
        ...(variantId && { variantId }),
        ...(type && { type }),
        ...(startDate && endDate && {
          createdAt: {
            gte: new Date(startDate),
            lte: new Date(endDate)
          }
        })
      };

      const [movements, total] = await Promise.all([
        prisma.stockMovement.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            product: {
              select: { name: true }
            },
            variant: {
              select: { sku: true, size: true, color: true }
            }
          }
        }),
        prisma.stockMovement.count({ where })
      ]);

      return {
        movements,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: limit
        }
      };
    } catch (error) {
      logger.error('Failed to get stock movements', {
        filters,
        error: error.message
      });
      throw error;
    }
  }

  // Get stock summary
  async getStockSummary() {
    try {
      const [
        totalProducts,
        totalVariants,
        totalStock,
        lowStockCount,
        outOfStockCount
      ] = await Promise.all([
        prisma.product.count({
          where: { status: 'ACTIVE' }
        }),
        prisma.productVariant.count({
          where: { product: { status: 'ACTIVE' } }
        }),
        prisma.productVariant.aggregate({
          where: { product: { status: 'ACTIVE' } },
          _sum: { stock: true }
        }),
        prisma.productVariant.count({
          where: {
            stock: { lte: this.lowStockThreshold, gt: this.outOfStockThreshold },
            product: { status: 'ACTIVE' }
          }
        }),
        prisma.productVariant.count({
          where: {
            stock: { lte: this.outOfStockThreshold },
            product: { status: 'ACTIVE' }
          }
        })
      ]);

      return {
        totalProducts,
        totalVariants,
        totalStock: totalStock._sum.stock || 0,
        lowStockCount,
        outOfStockCount,
        stockValue: await this.calculateStockValue()
      };
    } catch (error) {
      logger.error('Failed to get stock summary', {
        error: error.message
      });
      throw error;
    }
  }

  // Calculate total stock value
  async calculateStockValue() {
    try {
      const variants = await prisma.productVariant.findMany({
        where: { product: { status: 'ACTIVE' } },
        select: { stock: true, costPrice: true, price: true }
      });

      let totalCostValue = 0;
      let totalRetailValue = 0;

      variants.forEach(variant => {
        const costPrice = variant.costPrice || variant.price * 0.6; // Assume 60% cost if not set
        totalCostValue += variant.stock * costPrice;
        totalRetailValue += variant.stock * variant.price;
      });

      return {
        costValue: Math.round(totalCostValue * 100) / 100,
        retailValue: Math.round(totalRetailValue * 100) / 100
      };
    } catch (error) {
      logger.error('Failed to calculate stock value', {
        error: error.message
      });
      return { costValue: 0, retailValue: 0 };
    }
  }

  // Set stock thresholds
  setThresholds(lowStock, outOfStock) {
    this.lowStockThreshold = lowStock;
    this.outOfStockThreshold = outOfStock;
    
    logger.info('Stock thresholds updated', {
      lowStock,
      outOfStock
    });
  }
}

module.exports = new StockService();

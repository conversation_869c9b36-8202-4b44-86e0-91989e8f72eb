import React from 'react';
import { useParams } from 'react-router-dom';

const CategoryPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();

  return (
    <div className="min-h-screen py-8">
      <div className="container-custom">
        <div className="bg-white rounded-lg shadow-soft p-8 text-center">
          <h1 className="text-2xl font-bold mb-4">Catégorie: {slug}</h1>
          <p className="text-secondary-600">
            Cette page affichera tous les produits de cette catégorie.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CategoryPage;

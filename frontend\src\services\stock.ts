import { apiClient, buildQueryString } from './api';
import type { ApiResponse, PaginatedResponse } from '@/types';

// Types for stock management
export interface StockMovement {
  id: string;
  productId: string;
  variantId: string;
  type: 'in' | 'out' | 'adjustment';
  quantity: number;
  previousStock: number;
  newStock: number;
  reason?: string;
  reference?: string;
  createdAt: string;
  product: {
    name: string;
  };
  variant: {
    sku: string;
    size?: string;
    color?: string;
  };
}

export interface StockSummary {
  totalProducts: number;
  totalVariants: number;
  totalStock: number;
  lowStockCount: number;
  outOfStockCount: number;
  stockValue: {
    costValue: number;
    retailValue: number;
  };
}

export interface LowStockProduct {
  id: string;
  sku: string;
  size?: string;
  color?: string;
  stock: number;
  product: {
    id: string;
    name: string;
    slug: string;
    images: Array<{
      url: string;
    }>;
  };
}

export interface StockUpdateRequest {
  quantity: number;
  type: 'in' | 'out' | 'adjustment';
  reason?: string;
  reference?: string;
}

export interface BulkStockUpdate {
  variantId: string;
  quantity: number;
  type: 'in' | 'out' | 'adjustment';
  reason?: string;
  reference?: string;
}

export interface StockReservation {
  id: string;
  variantId: string;
  quantity: number;
  expiresAt: string;
  createdAt: string;
}

export interface StockFilters {
  productId?: string;
  variantId?: string;
  type?: 'in' | 'out' | 'adjustment';
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

// Stock service
export const stockService = {
  // Get stock summary
  getStockSummary: async (): Promise<StockSummary> => {
    const response = await apiClient.get<ApiResponse<{ summary: StockSummary }>>('/stock/summary');
    
    if (response.data?.summary) {
      return response.data.summary;
    }
    
    throw new Error('Failed to get stock summary');
  },

  // Get low stock products
  getLowStockProducts: async (threshold?: number): Promise<LowStockProduct[]> => {
    const params = threshold ? `?threshold=${threshold}` : '';
    const response = await apiClient.get<ApiResponse<{ products: LowStockProduct[] }>>(`/stock/low-stock${params}`);
    
    if (response.data?.products) {
      return response.data.products;
    }
    
    return [];
  },

  // Get stock movements
  getStockMovements: async (filters: StockFilters = {}): Promise<PaginatedResponse<StockMovement>> => {
    const queryString = buildQueryString(filters);
    const url = queryString ? `/stock/movements?${queryString}` : '/stock/movements';
    
    const response = await apiClient.get<ApiResponse<PaginatedResponse<StockMovement>>>(url);
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Failed to get stock movements');
  },

  // Update stock for variant
  updateStock: async (variantId: string, updateData: StockUpdateRequest): Promise<any> => {
    const response = await apiClient.post<ApiResponse<{ variant: any }>>(
      `/stock/variants/${variantId}/update`,
      updateData
    );
    
    if (response.data?.variant) {
      return response.data.variant;
    }
    
    throw new Error('Failed to update stock');
  },

  // Bulk update stock
  bulkUpdateStock: async (updates: BulkStockUpdate[]): Promise<any[]> => {
    const response = await apiClient.post<ApiResponse<{ variants: any[] }>>(
      '/stock/bulk-update',
      { updates }
    );
    
    if (response.data?.variants) {
      return response.data.variants;
    }
    
    throw new Error('Failed to bulk update stock');
  },

  // Reserve stock
  reserveStock: async (items: Array<{ variantId: string; quantity: number }>): Promise<StockReservation[]> => {
    const response = await apiClient.post<ApiResponse<{ reservations: StockReservation[] }>>(
      '/stock/reserve',
      { items }
    );
    
    if (response.data?.reservations) {
      return response.data.reservations;
    }
    
    throw new Error('Failed to reserve stock');
  },

  // Release stock reservation
  releaseReservation: async (reservationId: string): Promise<void> => {
    await apiClient.delete<ApiResponse>(`/stock/reservations/${reservationId}`);
  },

  // Cleanup expired reservations
  cleanupExpiredReservations: async (): Promise<number> => {
    const response = await apiClient.post<ApiResponse<{ cleanedCount: number }>>(
      '/stock/cleanup-reservations'
    );
    
    if (response.data?.cleanedCount !== undefined) {
      return response.data.cleanedCount;
    }
    
    throw new Error('Failed to cleanup reservations');
  },

  // Set stock thresholds
  setStockThresholds: async (lowStock: number, outOfStock: number): Promise<void> => {
    await apiClient.post<ApiResponse>('/stock/thresholds', {
      lowStock,
      outOfStock
    });
  }
};

// Stock utilities
export const stockUtils = {
  // Get stock status
  getStockStatus: (stock: number, lowThreshold: number = 5): 'in_stock' | 'low_stock' | 'out_of_stock' => {
    if (stock === 0) return 'out_of_stock';
    if (stock <= lowThreshold) return 'low_stock';
    return 'in_stock';
  },

  // Get stock status color
  getStockStatusColor: (stock: number, lowThreshold: number = 5): string => {
    const status = stockUtils.getStockStatus(stock, lowThreshold);
    
    const colors = {
      in_stock: 'success',
      low_stock: 'warning',
      out_of_stock: 'error'
    };
    
    return colors[status];
  },

  // Get stock status label
  getStockStatusLabel: (stock: number, lowThreshold: number = 5): string => {
    const status = stockUtils.getStockStatus(stock, lowThreshold);
    
    const labels = {
      in_stock: 'En stock',
      low_stock: 'Stock faible',
      out_of_stock: 'Rupture de stock'
    };
    
    return labels[status];
  },

  // Format stock movement type
  formatMovementType: (type: string): string => {
    const types = {
      in: 'Entrée',
      out: 'Sortie',
      adjustment: 'Ajustement'
    };
    
    return types[type as keyof typeof types] || type;
  },

  // Get movement type color
  getMovementTypeColor: (type: string): string => {
    const colors = {
      in: 'success',
      out: 'error',
      adjustment: 'warning'
    };
    
    return colors[type as keyof typeof colors] || 'secondary';
  },

  // Calculate stock value
  calculateStockValue: (stock: number, price: number): number => {
    return stock * price;
  },

  // Format currency
  formatCurrency: (amount: number, currency: string = 'TND'): string => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  },

  // Validate stock update
  validateStockUpdate: (updateData: Partial<StockUpdateRequest>) => {
    const errors: string[] = [];
    
    if (updateData.quantity === undefined || updateData.quantity < 0) {
      errors.push('Quantité invalide');
    }
    
    if (!updateData.type) {
      errors.push('Type de mouvement requis');
    }
    
    if (!['in', 'out', 'adjustment'].includes(updateData.type!)) {
      errors.push('Type de mouvement invalide');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Get stock movement type options
  getMovementTypeOptions: () => [
    { value: 'in', label: 'Entrée de stock' },
    { value: 'out', label: 'Sortie de stock' },
    { value: 'adjustment', label: 'Ajustement' }
  ],

  // Calculate stock turnover
  calculateStockTurnover: (soldQuantity: number, averageStock: number): number => {
    if (averageStock === 0) return 0;
    return soldQuantity / averageStock;
  },

  // Get stock level percentage
  getStockLevelPercentage: (currentStock: number, maxStock: number): number => {
    if (maxStock === 0) return 0;
    return Math.min(100, (currentStock / maxStock) * 100);
  },

  // Check if stock is sufficient
  isStockSufficient: (requiredQuantity: number, availableStock: number): boolean => {
    return availableStock >= requiredQuantity;
  },

  // Calculate reorder point
  calculateReorderPoint: (dailySales: number, leadTimeDays: number, safetyStock: number = 0): number => {
    return (dailySales * leadTimeDays) + safetyStock;
  },

  // Format stock quantity
  formatStockQuantity: (quantity: number, unit: string = 'pcs'): string => {
    return `${quantity} ${unit}`;
  }
};

export default stockService;

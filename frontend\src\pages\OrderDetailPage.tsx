import React from 'react';
import { useParams } from 'react-router-dom';

const OrderDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="min-h-screen py-8">
      <div className="container-custom">
        <div className="bg-white rounded-lg shadow-soft p-8 text-center">
          <h1 className="text-2xl font-bold mb-4">Détail de la Commande</h1>
          <p className="text-secondary-600 mb-4">
            Commande: {id}
          </p>
          <p className="text-secondary-600">
            Page de détail de commande en cours de développement.
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailPage;

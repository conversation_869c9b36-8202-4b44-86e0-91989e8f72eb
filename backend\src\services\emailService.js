const nodemailer = require('nodemailer');
const config = require('../config/config');
const logger = require('../utils/logger');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: config.smtp.host,
      port: config.smtp.port,
      secure: config.smtp.port === 465,
      auth: config.smtp.auth
    });

    // Verify connection
    this.verifyConnection();
  }

  async verifyConnection() {
    try {
      await this.transporter.verify();
      logger.info('Email service connected successfully');
    } catch (error) {
      logger.error('Email service connection failed:', error);
    }
  }

  async sendEmail(to, subject, html, text = null) {
    try {
      const mailOptions = {
        from: `${config.fromName} <${config.fromEmail}>`,
        to,
        subject,
        html,
        text: text || html.replace(/<[^>]*>/g, '') // Strip HTML for text version
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info('Email sent successfully', {
        to,
        subject,
        messageId: result.messageId
      });

      return result;
    } catch (error) {
      logger.error('Failed to send email', {
        to,
        subject,
        error: error.message
      });
      throw error;
    }
  }

  // Email templates
  getEmailTemplate(title, content, buttonText = null, buttonUrl = null) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9fafb; }
          .button { 
            display: inline-block; 
            background: #2563eb; 
            color: white; 
            padding: 12px 30px; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 20px 0; 
          }
          .footer { 
            background: #374151; 
            color: #9ca3af; 
            padding: 20px; 
            text-align: center; 
            font-size: 14px; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🛍️ SassouFashion</h1>
          </div>
          <div class="content">
            <h2>${title}</h2>
            ${content}
            ${buttonText && buttonUrl ? `
              <div style="text-align: center;">
                <a href="${buttonUrl}" class="button">${buttonText}</a>
              </div>
            ` : ''}
          </div>
          <div class="footer">
            <p>© 2024 SassouFashion. Tous droits réservés.</p>
            <p>Boutique de mode en ligne - Tunisie</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Verification email
  async sendVerificationEmail(email, token, firstName) {
    const verificationUrl = `${config.appUrl}/verify-email?token=${token}`;
    
    const content = `
      <p>Bonjour ${firstName},</p>
      <p>Merci de vous être inscrit sur SassouFashion ! Pour activer votre compte, veuillez cliquer sur le bouton ci-dessous :</p>
      <p>Ce lien expire dans 24 heures.</p>
    `;

    const html = this.getEmailTemplate(
      'Vérifiez votre adresse email',
      content,
      'Vérifier mon email',
      verificationUrl
    );

    return this.sendEmail(email, 'Vérifiez votre adresse email - SassouFashion', html);
  }

  // Password reset email
  async sendPasswordResetEmail(email, token, firstName) {
    const resetUrl = `${config.appUrl}/reset-password?token=${token}`;
    
    const content = `
      <p>Bonjour ${firstName},</p>
      <p>Vous avez demandé la réinitialisation de votre mot de passe. Cliquez sur le bouton ci-dessous pour créer un nouveau mot de passe :</p>
      <p>Si vous n'avez pas demandé cette réinitialisation, ignorez cet email.</p>
      <p>Ce lien expire dans 1 heure.</p>
    `;

    const html = this.getEmailTemplate(
      'Réinitialisation de mot de passe',
      content,
      'Réinitialiser mon mot de passe',
      resetUrl
    );

    return this.sendEmail(email, 'Réinitialisation de mot de passe - SassouFashion', html);
  }

  // Order confirmation email
  async sendOrderConfirmationEmail(email, order, firstName) {
    const orderUrl = `${config.appUrl}/orders/${order.id}`;
    
    const itemsList = order.items.map(item => `
      <tr>
        <td>${item.variant.product.name} (${item.variant.size || ''} ${item.variant.color || ''})</td>
        <td>${item.quantity}</td>
        <td>${item.price} TND</td>
        <td>${item.total} TND</td>
      </tr>
    `).join('');

    const content = `
      <p>Bonjour ${firstName},</p>
      <p>Votre commande <strong>#${order.orderNumber}</strong> a été confirmée !</p>
      
      <h3>Détails de la commande :</h3>
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background: #f3f4f6;">
            <th style="padding: 10px; border: 1px solid #ddd;">Produit</th>
            <th style="padding: 10px; border: 1px solid #ddd;">Quantité</th>
            <th style="padding: 10px; border: 1px solid #ddd;">Prix unitaire</th>
            <th style="padding: 10px; border: 1px solid #ddd;">Total</th>
          </tr>
        </thead>
        <tbody>
          ${itemsList}
        </tbody>
      </table>
      
      <p><strong>Sous-total :</strong> ${order.subtotal} TND</p>
      <p><strong>Frais de livraison :</strong> ${order.shippingCost} TND</p>
      <p><strong>Total :</strong> ${order.total} TND</p>
      
      <p>Nous préparons votre commande et vous tiendrons informé de son statut.</p>
    `;

    const html = this.getEmailTemplate(
      'Confirmation de commande',
      content,
      'Voir ma commande',
      orderUrl
    );

    return this.sendEmail(email, `Commande confirmée #${order.orderNumber} - SassouFashion`, html);
  }

  // Order shipped email
  async sendOrderShippedEmail(email, order, firstName, trackingNumber) {
    const trackingUrl = order.shipping?.carrier === 'poste' 
      ? `https://www.poste.tn/suivi-colis?tracking=${trackingNumber}`
      : `${config.appUrl}/orders/${order.id}`;
    
    const content = `
      <p>Bonjour ${firstName},</p>
      <p>Bonne nouvelle ! Votre commande <strong>#${order.orderNumber}</strong> a été expédiée.</p>
      
      ${trackingNumber ? `
        <p><strong>Numéro de suivi :</strong> ${trackingNumber}</p>
        <p>Vous pouvez suivre votre colis en temps réel.</p>
      ` : ''}
      
      <p>Livraison estimée : ${order.shipping?.estimatedDelivery ? 
        new Date(order.shipping.estimatedDelivery).toLocaleDateString('fr-FR') : 
        '2-3 jours ouvrables'}</p>
    `;

    const html = this.getEmailTemplate(
      'Commande expédiée',
      content,
      'Suivre ma commande',
      trackingUrl
    );

    return this.sendEmail(email, `Commande expédiée #${order.orderNumber} - SassouFashion`, html);
  }

  // Newsletter
  async sendNewsletter(email, subject, content, firstName = '') {
    const greeting = firstName ? `Bonjour ${firstName},` : 'Bonjour,';
    
    const fullContent = `
      <p>${greeting}</p>
      ${content}
      <p>L'équipe SassouFashion</p>
    `;

    const html = this.getEmailTemplate(subject, fullContent);
    return this.sendEmail(email, `${subject} - SassouFashion`, html);
  }

  // Promotional email
  async sendPromotionalEmail(email, promotion, firstName = '') {
    const content = `
      <p>Bonjour ${firstName ? firstName : ''},</p>
      <p>🎉 <strong>${promotion.name}</strong></p>
      <p>${promotion.description}</p>
      
      ${promotion.code ? `
        <div style="background: #fef3c7; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
          <p><strong>Code promo :</strong> <span style="font-size: 18px; color: #d97706;">${promotion.code}</span></p>
        </div>
      ` : ''}
      
      <p>Offre valable jusqu'au ${new Date(promotion.endDate).toLocaleDateString('fr-FR')}</p>
      <p>Ne manquez pas cette opportunité !</p>
    `;

    const html = this.getEmailTemplate(
      promotion.name,
      content,
      'Découvrir les offres',
      `${config.appUrl}/promotions`
    );

    return this.sendEmail(email, `${promotion.name} - SassouFashion`, html);
  }
}

module.exports = new EmailService();

const logger = require('../utils/logger');
const config = require('../config/config');

// Custom error class
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Prisma error handler
const handlePrismaError = (error) => {
  let message = 'Erreur de base de données';
  let statusCode = 500;

  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const field = error.meta?.target?.[0] || 'champ';
      message = `Cette valeur pour ${field} existe déjà`;
      statusCode = 409;
      break;
    
    case 'P2025':
      // Record not found
      message = 'Enregistrement non trouvé';
      statusCode = 404;
      break;
    
    case 'P2003':
      // Foreign key constraint violation
      message = 'Impossible de supprimer cet élément car il est utilisé ailleurs';
      statusCode = 409;
      break;
    
    case 'P2014':
      // Required relation missing
      message = 'Relation requise manquante';
      statusCode = 400;
      break;
    
    case 'P2021':
      // Table does not exist
      message = 'Table de base de données non trouvée';
      statusCode = 500;
      break;
    
    case 'P2022':
      // Column does not exist
      message = 'Colonne de base de données non trouvée';
      statusCode = 500;
      break;
    
    default:
      message = 'Erreur de base de données interne';
      statusCode = 500;
  }

  return new AppError(message, statusCode, error.code);
};

// JWT error handler
const handleJWTError = () => {
  return new AppError('Token invalide. Veuillez vous reconnecter.', 401, 'INVALID_TOKEN');
};

const handleJWTExpiredError = () => {
  return new AppError('Token expiré. Veuillez vous reconnecter.', 401, 'EXPIRED_TOKEN');
};

// Validation error handler
const handleValidationError = (error) => {
  const errors = error.details?.map(detail => detail.message) || [error.message];
  const message = `Données invalides: ${errors.join('. ')}`;
  return new AppError(message, 400, 'VALIDATION_ERROR');
};

// Cast error handler (for invalid ObjectIds, etc.)
const handleCastError = (error) => {
  const message = `Valeur invalide pour ${error.path}: ${error.value}`;
  return new AppError(message, 400, 'CAST_ERROR');
};

// Multer error handler
const handleMulterError = (error) => {
  let message = 'Erreur de téléchargement de fichier';
  let statusCode = 400;

  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      message = 'Fichier trop volumineux';
      break;
    case 'LIMIT_FILE_COUNT':
      message = 'Trop de fichiers';
      break;
    case 'LIMIT_UNEXPECTED_FILE':
      message = 'Champ de fichier inattendu';
      break;
    default:
      message = error.message || 'Erreur de téléchargement';
  }

  return new AppError(message, statusCode, error.code);
};

// Send error response in development
const sendErrorDev = (err, req, res) => {
  // Log error
  logger.logError(err, req);

  // API error
  if (req.originalUrl.startsWith('/api')) {
    return res.status(err.statusCode).json({
      status: err.status,
      error: err,
      message: err.message,
      stack: err.stack,
      code: err.code
    });
  }

  // Rendered website error
  res.status(err.statusCode).json({
    title: 'Erreur',
    message: err.message,
    error: err
  });
};

// Send error response in production
const sendErrorProd = (err, req, res) => {
  // API error
  if (req.originalUrl.startsWith('/api')) {
    // Operational, trusted error: send message to client
    if (err.isOperational) {
      return res.status(err.statusCode).json({
        status: err.status,
        message: err.message,
        code: err.code
      });
    }

    // Programming or other unknown error: don't leak error details
    logger.logError(err, req);
    
    return res.status(500).json({
      status: 'error',
      message: 'Une erreur interne s\'est produite',
      code: 'INTERNAL_ERROR'
    });
  }

  // Rendered website error
  if (err.isOperational) {
    return res.status(err.statusCode).json({
      title: 'Erreur',
      message: err.message
    });
  }

  // Programming or other unknown error
  logger.logError(err, req);
  
  res.status(500).json({
    title: 'Erreur',
    message: 'Une erreur interne s\'est produite'
  });
};

// Global error handling middleware
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Prisma errors
  if (err.code && err.code.startsWith('P')) {
    error = handlePrismaError(error);
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = handleJWTError();
  }
  if (err.name === 'TokenExpiredError') {
    error = handleJWTExpiredError();
  }

  // Validation errors (Joi)
  if (err.name === 'ValidationError' && err.details) {
    error = handleValidationError(error);
  }

  // Cast errors
  if (err.name === 'CastError') {
    error = handleCastError(error);
  }

  // Multer errors
  if (err.code && ['LIMIT_FILE_SIZE', 'LIMIT_FILE_COUNT', 'LIMIT_UNEXPECTED_FILE'].includes(err.code)) {
    error = handleMulterError(error);
  }

  // Set default values
  error.statusCode = error.statusCode || 500;
  error.status = error.status || 'error';

  // Send error response
  if (config.nodeEnv === 'development') {
    sendErrorDev(error, req, res);
  } else {
    sendErrorProd(error, req, res);
  }
};

module.exports = {
  AppError,
  errorHandler
};

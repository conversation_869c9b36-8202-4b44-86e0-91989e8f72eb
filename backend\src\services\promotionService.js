const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const socketService = require('./socketService');

const prisma = new PrismaClient();

class PromotionService {
  // Apply promotion to product
  async applyPromotion(productId, promotionData) {
    try {
      const {
        name,
        description,
        type, // 'PERCENTAGE' or 'FIXED_AMOUNT'
        value,
        startDate,
        endDate,
        isActive = true
      } = promotionData;

      // Validate product exists
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: { variants: true }
      });

      if (!product) {
        throw new Error('Produit non trouvé');
      }

      // Create promotion
      const promotion = await prisma.promotion.create({
        data: {
          name,
          description,
          type,
          value,
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          isActive,
          products: {
            connect: { id: productId }
          }
        }
      });

      // Update product variants with promotional prices
      if (isActive && new Date() >= new Date(startDate) && new Date() <= new Date(endDate)) {
        await this.updatePromotionalPrices(productId, type, value);
      }

      logger.logBusiness('Promotion applied to product', {
        promotionId: promotion.id,
        productId,
        type,
        value
      });

      return promotion;
    } catch (error) {
      logger.error('Failed to apply promotion', {
        productId,
        error: error.message
      });
      throw error;
    }
  }

  // Apply promotion to category
  async applyCategoryPromotion(categoryId, promotionData) {
    try {
      const {
        name,
        description,
        type,
        value,
        startDate,
        endDate,
        isActive = true
      } = promotionData;

      // Validate category exists
      const category = await prisma.category.findUnique({
        where: { id: categoryId },
        include: { products: true }
      });

      if (!category) {
        throw new Error('Catégorie non trouvée');
      }

      // Create promotion
      const promotion = await prisma.promotion.create({
        data: {
          name,
          description,
          type,
          value,
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          isActive,
          categories: {
            connect: { id: categoryId }
          }
        }
      });

      // Apply to all products in category
      if (isActive && new Date() >= new Date(startDate) && new Date() <= new Date(endDate)) {
        for (const product of category.products) {
          await this.updatePromotionalPrices(product.id, type, value);
        }
      }

      logger.logBusiness('Promotion applied to category', {
        promotionId: promotion.id,
        categoryId,
        productCount: category.products.length
      });

      return promotion;
    } catch (error) {
      logger.error('Failed to apply category promotion', {
        categoryId,
        error: error.message
      });
      throw error;
    }
  }

  // Update promotional prices for product variants
  async updatePromotionalPrices(productId, type, value) {
    try {
      const variants = await prisma.productVariant.findMany({
        where: { productId }
      });

      for (const variant of variants) {
        let promotionalPrice;

        if (type === 'PERCENTAGE') {
          promotionalPrice = variant.price * (1 - value / 100);
        } else if (type === 'FIXED_AMOUNT') {
          promotionalPrice = Math.max(0, variant.price - value);
        } else {
          continue;
        }

        await prisma.productVariant.update({
          where: { id: variant.id },
          data: { 
            promotionalPrice: Math.round(promotionalPrice * 100) / 100 // Round to 2 decimals
          }
        });
      }

      logger.info('Promotional prices updated', {
        productId,
        variantCount: variants.length
      });
    } catch (error) {
      logger.error('Failed to update promotional prices', {
        productId,
        error: error.message
      });
      throw error;
    }
  }

  // Remove promotional prices
  async removePromotionalPrices(productId) {
    try {
      await prisma.productVariant.updateMany({
        where: { productId },
        data: { promotionalPrice: null }
      });

      logger.info('Promotional prices removed', { productId });
    } catch (error) {
      logger.error('Failed to remove promotional prices', {
        productId,
        error: error.message
      });
      throw error;
    }
  }

  // Get active promotions
  async getActivePromotions() {
    try {
      const now = new Date();

      const promotions = await prisma.promotion.findMany({
        where: {
          isActive: true,
          startDate: { lte: now },
          endDate: { gte: now }
        },
        include: {
          products: {
            select: { id: true, name: true, slug: true }
          },
          categories: {
            select: { id: true, name: true, slug: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return promotions;
    } catch (error) {
      logger.error('Failed to get active promotions', {
        error: error.message
      });
      throw error;
    }
  }

  // Get promotion by ID
  async getPromotion(promotionId) {
    try {
      const promotion = await prisma.promotion.findUnique({
        where: { id: promotionId },
        include: {
          products: {
            include: {
              variants: {
                select: { price: true, promotionalPrice: true }
              },
              images: {
                take: 1,
                orderBy: { sortOrder: 'asc' }
              }
            }
          },
          categories: {
            include: {
              products: {
                include: {
                  variants: {
                    select: { price: true, promotionalPrice: true }
                  },
                  images: {
                    take: 1,
                    orderBy: { sortOrder: 'asc' }
                  }
                }
              }
            }
          }
        }
      });

      return promotion;
    } catch (error) {
      logger.error('Failed to get promotion', {
        promotionId,
        error: error.message
      });
      throw error;
    }
  }

  // Activate/Deactivate promotion
  async togglePromotion(promotionId, isActive) {
    try {
      const promotion = await prisma.promotion.update({
        where: { id: promotionId },
        data: { isActive },
        include: {
          products: true,
          categories: {
            include: { products: true }
          }
        }
      });

      // Update promotional prices based on activation status
      const allProducts = [
        ...promotion.products,
        ...promotion.categories.flatMap(cat => cat.products)
      ];

      for (const product of allProducts) {
        if (isActive) {
          await this.updatePromotionalPrices(product.id, promotion.type, promotion.value);
        } else {
          await this.removePromotionalPrices(product.id);
        }
      }

      logger.logBusiness('Promotion toggled', {
        promotionId,
        isActive,
        affectedProducts: allProducts.length
      });

      return promotion;
    } catch (error) {
      logger.error('Failed to toggle promotion', {
        promotionId,
        error: error.message
      });
      throw error;
    }
  }

  // Check and activate/deactivate promotions based on dates
  async checkPromotionSchedules() {
    try {
      const now = new Date();

      // Activate promotions that should start
      const promotionsToActivate = await prisma.promotion.findMany({
        where: {
          isActive: false,
          startDate: { lte: now },
          endDate: { gte: now }
        },
        include: {
          products: true,
          categories: { include: { products: true } }
        }
      });

      for (const promotion of promotionsToActivate) {
        await this.togglePromotion(promotion.id, true);
        
        // Send notification
        socketService.sendPromotionNotification(promotion);
      }

      // Deactivate expired promotions
      const promotionsToDeactivate = await prisma.promotion.findMany({
        where: {
          isActive: true,
          endDate: { lt: now }
        },
        include: {
          products: true,
          categories: { include: { products: true } }
        }
      });

      for (const promotion of promotionsToDeactivate) {
        await this.togglePromotion(promotion.id, false);
      }

      logger.info('Promotion schedules checked', {
        activated: promotionsToActivate.length,
        deactivated: promotionsToDeactivate.length
      });

      return {
        activated: promotionsToActivate.length,
        deactivated: promotionsToDeactivate.length
      };
    } catch (error) {
      logger.error('Failed to check promotion schedules', {
        error: error.message
      });
      throw error;
    }
  }

  // Get promotion statistics
  async getPromotionStats(promotionId) {
    try {
      const promotion = await prisma.promotion.findUnique({
        where: { id: promotionId },
        include: {
          products: true,
          categories: { include: { products: true } }
        }
      });

      if (!promotion) {
        throw new Error('Promotion non trouvée');
      }

      const allProducts = [
        ...promotion.products,
        ...promotion.categories.flatMap(cat => cat.products)
      ];

      const productIds = allProducts.map(p => p.id);

      // Get sales data during promotion period
      const salesData = await prisma.orderItem.aggregate({
        where: {
          variant: {
            productId: { in: productIds }
          },
          order: {
            status: { in: ['DELIVERED', 'CONFIRMED'] },
            createdAt: {
              gte: promotion.startDate,
              lte: promotion.endDate
            }
          }
        },
        _sum: {
          quantity: true,
          total: true
        },
        _count: true
      });

      return {
        promotion,
        stats: {
          totalSales: salesData._sum.quantity || 0,
          totalRevenue: salesData._sum.total || 0,
          orderCount: salesData._count,
          affectedProducts: productIds.length
        }
      };
    } catch (error) {
      logger.error('Failed to get promotion stats', {
        promotionId,
        error: error.message
      });
      throw error;
    }
  }

  // Delete promotion
  async deletePromotion(promotionId) {
    try {
      const promotion = await prisma.promotion.findUnique({
        where: { id: promotionId },
        include: {
          products: true,
          categories: { include: { products: true } }
        }
      });

      if (!promotion) {
        throw new Error('Promotion non trouvée');
      }

      // Remove promotional prices first
      const allProducts = [
        ...promotion.products,
        ...promotion.categories.flatMap(cat => cat.products)
      ];

      for (const product of allProducts) {
        await this.removePromotionalPrices(product.id);
      }

      // Delete promotion
      await prisma.promotion.delete({
        where: { id: promotionId }
      });

      logger.logBusiness('Promotion deleted', {
        promotionId,
        affectedProducts: allProducts.length
      });

      return true;
    } catch (error) {
      logger.error('Failed to delete promotion', {
        promotionId,
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = new PromotionService();

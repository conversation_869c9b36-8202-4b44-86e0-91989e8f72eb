require('dotenv').config();

const config = {
  // Application
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT) || 5000,
  appName: process.env.APP_NAME || 'SassouFashion',
  appUrl: process.env.APP_URL || 'http://localhost:3000',
  apiUrl: process.env.API_URL || 'http://localhost:5000',

  // Database
  databaseUrl: process.env.DATABASE_URL,

  // Security
  jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
  bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,

  // CORS
  allowedOrigins: process.env.ALLOWED_ORIGINS 
    ? process.env.ALLOWED_ORIGINS.split(',')
    : ['http://localhost:3000', 'http://localhost:3001'],

  // Rate Limiting
  rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,

  // Email
  smtp: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT) || 587,
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  },
  fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
  fromName: process.env.FROM_NAME || 'SassouFashion',

  // SMS (Twilio)
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID,
    authToken: process.env.TWILIO_AUTH_TOKEN,
    phoneNumber: process.env.TWILIO_PHONE_NUMBER
  },

  // Cloudinary
  cloudinary: {
    cloudName: process.env.CLOUDINARY_CLOUD_NAME,
    apiKey: process.env.CLOUDINARY_API_KEY,
    apiSecret: process.env.CLOUDINARY_API_SECRET
  },

  // Payment Gateways
  stripe: {
    publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
    secretKey: process.env.STRIPE_SECRET_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET
  },

  paypal: {
    clientId: process.env.PAYPAL_CLIENT_ID,
    clientSecret: process.env.PAYPAL_CLIENT_SECRET,
    mode: process.env.PAYPAL_MODE || 'sandbox'
  },

  d17: {
    apiKey: process.env.D17_API_KEY,
    secretKey: process.env.D17_SECRET_KEY,
    environment: process.env.D17_ENVIRONMENT || 'sandbox'
  },

  edinar: {
    merchantId: process.env.EDINAR_MERCHANT_ID,
    secretKey: process.env.EDINAR_SECRET_KEY,
    environment: process.env.EDINAR_ENVIRONMENT || 'test'
  },

  // Shipping
  poste: {
    apiKey: process.env.POSTE_API_KEY,
    username: process.env.POSTE_USERNAME,
    password: process.env.POSTE_PASSWORD
  },

  aramex: {
    username: process.env.ARAMEX_USERNAME,
    password: process.env.ARAMEX_PASSWORD,
    accountNumber: process.env.ARAMEX_ACCOUNT_NUMBER,
    accountPin: process.env.ARAMEX_ACCOUNT_PIN,
    accountEntity: process.env.ARAMEX_ACCOUNT_ENTITY,
    accountCountryCode: process.env.ARAMEX_ACCOUNT_COUNTRY_CODE || 'TN'
  },

  // Redis
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB) || 0
  },

  // Upload
  upload: {
    maxSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 10 * 1024 * 1024, // 10MB
    allowedTypes: process.env.UPLOAD_ALLOWED_TYPES 
      ? process.env.UPLOAD_ALLOWED_TYPES.split(',')
      : ['image/jpeg', 'image/png', 'image/webp']
  },

  // Session
  session: {
    secret: process.env.SESSION_SECRET || 'your-session-secret',
    secure: process.env.COOKIE_SECURE === 'true',
    sameSite: process.env.COOKIE_SAME_SITE || 'lax'
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log'
  },

  // Social Media
  whatsapp: {
    token: process.env.WHATSAPP_TOKEN,
    phoneNumberId: process.env.WHATSAPP_PHONE_NUMBER_ID,
    verifyToken: process.env.WHATSAPP_VERIFY_TOKEN
  },

  facebook: {
    pageAccessToken: process.env.FACEBOOK_PAGE_ACCESS_TOKEN,
    verifyToken: process.env.FACEBOOK_VERIFY_TOKEN
  },

  // Monitoring
  sentry: {
    dsn: process.env.SENTRY_DSN
  },

  // Business Logic
  business: {
    // Loyalty points
    loyaltyPointsPerDinar: 1, // 1 point per 1 TND spent
    loyaltyPointsValue: 0.01, // 1 point = 0.01 TND
    
    // Shipping
    freeShippingThreshold: 100, // Free shipping above 100 TND
    defaultShippingCost: 7, // 7 TND default shipping
    
    // Stock
    lowStockThreshold: 10, // Alert when stock < 10
    
    // Orders
    orderNumberPrefix: 'SF',
    orderCancellationWindow: 24, // 24 hours to cancel
    
    // Reviews
    reviewCooldownDays: 7, // Can review 7 days after purchase
    
    // Promotions
    maxDiscountPercentage: 70, // Max 70% discount
    
    // Currency
    defaultCurrency: 'TND',
    supportedCurrencies: ['TND', 'EUR', 'USD']
  }
};

// Validation
const requiredEnvVars = [
  'DATABASE_URL',
  'JWT_SECRET'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingEnvVars.forEach(envVar => {
    console.error(`   - ${envVar}`);
  });
  console.error('Please check your .env file');
  process.exit(1);
}

module.exports = config;

const express = require('express');
const { body, param, query } = require('express-validator');
const { authenticate, authorize } = require('../middleware/auth');
const promotionService = require('../services/promotionService');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Validation middleware
const validatePromotion = [
  body('name')
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Le nom doit contenir entre 3 et 100 caractères'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('La description ne peut pas dépasser 500 caractères'),
  body('type')
    .isIn(['PERCENTAGE', 'FIXED_AMOUNT'])
    .withMessage('Type de promotion invalide'),
  body('value')
    .isFloat({ min: 0 })
    .withMessage('Valeur de promotion invalide'),
  body('startDate')
    .isISO8601()
    .withMessage('Date de début invalide'),
  body('endDate')
    .isISO8601()
    .withMessage('Date de fin invalide')
    .custom((endDate, { req }) => {
      if (new Date(endDate) <= new Date(req.body.startDate)) {
        throw new Error('La date de fin doit être après la date de début');
      }
      return true;
    })
];

const validateId = [
  param('id')
    .isUUID()
    .withMessage('ID invalide')
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    return next(new AppError(errorMessages.join(', '), 400, 'VALIDATION_ERROR'));
  }
  next();
};

// All routes require authentication and admin/employee role
router.use(authenticate);
router.use(authorize('ADMIN', 'EMPLOYEE'));

// @desc    Get all promotions
// @route   GET /api/promotions
// @access  Private (Admin/Employee)
router.get('/', async (req, res, next) => {
  try {
    const { active, page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    const where = {
      ...(active !== undefined && { isActive: active === 'true' })
    };

    const [promotions, total] = await Promise.all([
      prisma.promotion.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: 'desc' },
        include: {
          products: {
            select: { id: true, name: true, slug: true }
          },
          categories: {
            select: { id: true, name: true, slug: true }
          }
        }
      }),
      prisma.promotion.count({ where })
    ]);

    const totalPages = Math.ceil(total / take);

    res.json({
      status: 'success',
      data: {
        promotions,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: take
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Get active promotions (public)
// @route   GET /api/promotions/active
// @access  Public
router.get('/active', async (req, res, next) => {
  try {
    const promotions = await promotionService.getActivePromotions();

    res.json({
      status: 'success',
      data: { promotions }
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Get single promotion
// @route   GET /api/promotions/:id
// @access  Private (Admin/Employee)
router.get('/:id', validateId, handleValidationErrors, async (req, res, next) => {
  try {
    const { id } = req.params;
    const promotion = await promotionService.getPromotion(id);

    if (!promotion) {
      return next(new AppError('Promotion non trouvée', 404, 'PROMOTION_NOT_FOUND'));
    }

    res.json({
      status: 'success',
      data: { promotion }
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Create promotion for product
// @route   POST /api/promotions/product/:productId
// @access  Private (Admin/Employee)
router.post('/product/:productId', 
  [param('productId').isUUID().withMessage('ID de produit invalide')],
  validatePromotion,
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { productId } = req.params;
      const promotion = await promotionService.applyPromotion(productId, req.body);

      res.status(201).json({
        status: 'success',
        message: 'Promotion créée avec succès',
        data: { promotion }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @desc    Create promotion for category
// @route   POST /api/promotions/category/:categoryId
// @access  Private (Admin/Employee)
router.post('/category/:categoryId',
  [param('categoryId').isUUID().withMessage('ID de catégorie invalide')],
  validatePromotion,
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { categoryId } = req.params;
      const promotion = await promotionService.applyCategoryPromotion(categoryId, req.body);

      res.status(201).json({
        status: 'success',
        message: 'Promotion créée avec succès',
        data: { promotion }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @desc    Toggle promotion status
// @route   PATCH /api/promotions/:id/toggle
// @access  Private (Admin/Employee)
router.patch('/:id/toggle',
  validateId,
  [body('isActive').isBoolean().withMessage('Statut invalide')],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { id } = req.params;
      const { isActive } = req.body;

      const promotion = await promotionService.togglePromotion(id, isActive);

      res.json({
        status: 'success',
        message: `Promotion ${isActive ? 'activée' : 'désactivée'} avec succès`,
        data: { promotion }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @desc    Get promotion statistics
// @route   GET /api/promotions/:id/stats
// @access  Private (Admin/Employee)
router.get('/:id/stats', validateId, handleValidationErrors, async (req, res, next) => {
  try {
    const { id } = req.params;
    const stats = await promotionService.getPromotionStats(id);

    res.json({
      status: 'success',
      data: stats
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Delete promotion
// @route   DELETE /api/promotions/:id
// @access  Private (Admin)
router.delete('/:id', 
  authorize('ADMIN'),
  validateId, 
  handleValidationErrors, 
  async (req, res, next) => {
    try {
      const { id } = req.params;
      await promotionService.deletePromotion(id);

      res.json({
        status: 'success',
        message: 'Promotion supprimée avec succès'
      });

    } catch (error) {
      next(error);
    }
  }
);

// @desc    Check promotion schedules (cron job endpoint)
// @route   POST /api/promotions/check-schedules
// @access  Private (Admin)
router.post('/check-schedules', authorize('ADMIN'), async (req, res, next) => {
  try {
    const result = await promotionService.checkPromotionSchedules();

    res.json({
      status: 'success',
      message: 'Vérification des promotions terminée',
      data: result
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;

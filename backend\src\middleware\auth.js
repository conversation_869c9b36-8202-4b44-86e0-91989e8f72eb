const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const { AppError } = require('./errorHandler');
const config = require('../config/config');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

// Generate JWT token
const generateToken = (payload) => {
  return jwt.sign(payload, config.jwtSecret, {
    expiresIn: config.jwtExpiresIn
  });
};

// Verify JWT token
const verifyToken = (token) => {
  return jwt.verify(token, config.jwtSecret);
};

// Hash password
const hashPassword = async (password) => {
  return await bcrypt.hash(password, config.bcryptRounds);
};

// Compare password
const comparePassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword);
};

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(new AppError('Token d\'accès requis', 401, 'NO_TOKEN'));
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    const decoded = verifyToken(token);

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        isVerified: true,
        lastLoginAt: true
      }
    });

    if (!user) {
      return next(new AppError('Utilisateur non trouvé', 401, 'USER_NOT_FOUND'));
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      logger.logSecurity('Inactive user login attempt', {
        userId: user.id,
        email: user.email,
        status: user.status,
        ip: req.ip
      });
      return next(new AppError('Compte désactivé', 401, 'ACCOUNT_INACTIVE'));
    }

    // Check if user is verified (for clients)
    if (user.role === 'CLIENT' && !user.isVerified) {
      return next(new AppError('Compte non vérifié', 401, 'ACCOUNT_NOT_VERIFIED'));
    }

    // Update last login time
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    });

    // Add user to request object
    req.user = user;
    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      logger.logSecurity('Invalid token attempt', {
        token: req.headers.authorization,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      return next(new AppError('Token invalide', 401, 'INVALID_TOKEN'));
    }
    
    if (error.name === 'TokenExpiredError') {
      return next(new AppError('Token expiré', 401, 'EXPIRED_TOKEN'));
    }

    next(error);
  }
};

// Authorization middleware - check user roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Authentification requise', 401, 'AUTH_REQUIRED'));
    }

    if (!roles.includes(req.user.role)) {
      logger.logSecurity('Unauthorized access attempt', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: roles,
        route: req.originalUrl,
        method: req.method,
        ip: req.ip
      });
      
      return next(new AppError('Accès non autorisé', 403, 'INSUFFICIENT_PERMISSIONS'));
    }

    next();
  };
};

// Optional authentication - doesn't fail if no token
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);

    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        isVerified: true
      }
    });

    if (user && user.status === 'ACTIVE') {
      req.user = user;
    }

    next();
  } catch (error) {
    // Ignore auth errors in optional auth
    next();
  }
};

// Check if user owns resource or is admin
const checkOwnership = (resourceUserIdField = 'userId') => {
  return async (req, res, next) => {
    try {
      // Admins can access everything
      if (req.user.role === 'ADMIN') {
        return next();
      }

      // Get resource ID from params
      const resourceId = req.params.id;
      
      if (!resourceId) {
        return next(new AppError('ID de ressource requis', 400, 'RESOURCE_ID_REQUIRED'));
      }

      // This is a generic check - specific implementations should override this
      // For now, we'll assume the resource has a userId field
      const resource = await prisma[req.resourceModel].findUnique({
        where: { id: resourceId },
        select: { [resourceUserIdField]: true }
      });

      if (!resource) {
        return next(new AppError('Ressource non trouvée', 404, 'RESOURCE_NOT_FOUND'));
      }

      if (resource[resourceUserIdField] !== req.user.id) {
        logger.logSecurity('Unauthorized resource access attempt', {
          userId: req.user.id,
          resourceId,
          resourceModel: req.resourceModel,
          route: req.originalUrl,
          ip: req.ip
        });
        
        return next(new AppError('Accès non autorisé à cette ressource', 403, 'RESOURCE_ACCESS_DENIED'));
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// Rate limiting for authentication endpoints
const authRateLimit = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    error: 'Trop de tentatives de connexion. Réessayez dans 15 minutes.',
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.logSecurity('Auth rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      route: req.originalUrl
    });
    
    res.status(429).json({
      status: 'error',
      message: 'Trop de tentatives de connexion. Réessayez dans 15 minutes.',
      code: 'RATE_LIMIT_EXCEEDED',
      retryAfter: 15 * 60
    });
  }
};

// Validate password strength
const validatePassword = (password) => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const errors = [];

  if (password.length < minLength) {
    errors.push(`Le mot de passe doit contenir au moins ${minLength} caractères`);
  }
  if (!hasUpperCase) {
    errors.push('Le mot de passe doit contenir au moins une majuscule');
  }
  if (!hasLowerCase) {
    errors.push('Le mot de passe doit contenir au moins une minuscule');
  }
  if (!hasNumbers) {
    errors.push('Le mot de passe doit contenir au moins un chiffre');
  }
  if (!hasSpecialChar) {
    errors.push('Le mot de passe doit contenir au moins un caractère spécial');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

module.exports = {
  generateToken,
  verifyToken,
  hashPassword,
  comparePassword,
  authenticate,
  authorize,
  optionalAuth,
  checkOwnership,
  authRateLimit,
  validatePassword
};

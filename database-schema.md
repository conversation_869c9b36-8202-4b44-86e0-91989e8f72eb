# 🗄️ Schéma de Base de Données - SassouFashion

## 📊 Diagramme Relationnel

```mermaid
erDiagram
    User ||--o{ Order : places
    User ||--o{ Address : has
    User ||--o{ Review : writes
    User ||--o{ CartItem : owns
    User ||--o{ LoyaltyPoint : earns
    
    Category ||--o{ Product : contains
    Product ||--o{ ProductVariant : has
    Product ||--o{ ProductImage : has
    Product ||--o{ Review : receives
    Product ||--o{ CartItem : contains
    Product ||--o{ StockMovement : tracks
    
    ProductVariant ||--o{ OrderItem : ordered
    ProductVariant ||--o{ CartItem : selected
    
    Order ||--o{ OrderItem : contains
    Order ||--|| Payment : has
    Order ||--|| Shipping : has
    
    Promotion ||--o{ Product : applies_to
    Promotion ||--o{ Order : used_in
    
    Coupon ||--o{ Order : used_in
```

## 🏗️ Entités Principales

### 👤 Gestion des Utilisateurs
- **User** : Clients, employés, administrateurs
- **Address** : Adresses de livraison multiples
- **LoyaltyPoint** : Système de fidélité

### 🛍️ Gestion des Produits
- **Category** : Catégories hiérarchiques
- **Product** : Produits principaux
- **ProductVariant** : Variations (taille, couleur)
- **ProductImage** : Images multiples par produit
- **StockMovement** : Journal des mouvements de stock

### 🧾 Gestion des Commandes
- **Order** : Commandes principales
- **OrderItem** : Articles dans les commandes
- **CartItem** : Panier temporaire
- **Payment** : Informations de paiement
- **Shipping** : Informations de livraison

### 🎯 Marketing & Promotions
- **Promotion** : Promotions sur produits
- **Coupon** : Codes de réduction
- **Newsletter** : Abonnements newsletter

### 💬 Support Client
- **Review** : Avis clients
- **SupportTicket** : Tickets de support
- **Message** : Messages du chat

### 📊 Administration
- **AuditLog** : Journal d'activité
- **Notification** : Notifications système

## 🔑 Relations Clés

### Relations Utilisateur
- Un utilisateur peut avoir plusieurs adresses
- Un utilisateur peut passer plusieurs commandes
- Un utilisateur peut laisser plusieurs avis

### Relations Produit
- Un produit appartient à une catégorie
- Un produit peut avoir plusieurs variantes
- Un produit peut avoir plusieurs images
- Un produit peut recevoir plusieurs avis

### Relations Commande
- Une commande contient plusieurs articles
- Une commande a un paiement unique
- Une commande a une livraison unique
- Une commande peut utiliser un coupon

## 📈 Optimisations Prévues

### Index de Performance
- Index sur `product.category_id`
- Index sur `order.user_id`
- Index sur `order.status`
- Index sur `product_variant.sku`
- Index composé sur `(product_id, size, color)`

### Contraintes de Données
- Stock ne peut pas être négatif
- Prix doivent être positifs
- Email unique par utilisateur
- SKU unique par variante

### Triggers et Procédures
- Mise à jour automatique du stock lors des commandes
- Calcul automatique des points de fidélité
- Génération automatique des numéros de commande
- Archivage automatique des anciennes données

## 🔒 Sécurité des Données

### Chiffrement
- Mots de passe hashés avec bcrypt
- Données sensibles chiffrées (numéros de carte)
- Tokens JWT sécurisés

### Audit Trail
- Toutes les modifications importantes loggées
- Traçabilité des actions administrateur
- Historique des changements de prix

### Sauvegarde
- Sauvegarde quotidienne automatique
- Réplication en temps réel
- Plan de récupération d'urgence

import React from 'react';
import { Outlet } from 'react-router-dom';

const AdminLayout: React.FC = () => {
  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white shadow-sm border-r border-secondary-200 min-h-screen">
          <div className="p-6">
            <h2 className="text-xl font-bold text-secondary-900">Administration</h2>
          </div>
          <nav className="px-4 space-y-2">
            <a href="/admin" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-100 rounded-lg">
              Dashboard
            </a>
            <a href="/admin/products" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-100 rounded-lg">
              Produits
            </a>
            <a href="/admin/orders" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-100 rounded-lg">
              Commandes
            </a>
            <a href="/admin/users" className="block px-4 py-2 text-secondary-700 hover:bg-secondary-100 rounded-lg">
              Utilisateurs
            </a>
          </nav>
        </aside>

        {/* Main content */}
        <main className="flex-1 p-8">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;

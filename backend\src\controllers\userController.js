const { PrismaClient } = require('@prisma/client');
const { AppError } = require('../middleware/errorHandler');
const { hashPassword, comparePassword } = require('../middleware/auth');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

class UserController {
  // @desc    Get user profile
  // @route   GET /api/users/profile
  // @access  Private
  async getProfile(req, res, next) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          avatar: true,
          role: true,
          status: true,
          isVerified: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
          addresses: {
            orderBy: { isDefault: 'desc' }
          },
          loyaltyPoints: {
            where: { type: 'earned' },
            orderBy: { createdAt: 'desc' },
            take: 10
          },
          _count: {
            select: {
              orders: true,
              reviews: true
            }
          }
        }
      });

      if (!user) {
        return next(new AppError('Utilisateur non trouvé', 404, 'USER_NOT_FOUND'));
      }

      // Calculate total loyalty points
      const totalPoints = await prisma.loyaltyPoint.aggregate({
        where: {
          userId: req.user.id,
          type: { in: ['earned', 'redeemed'] }
        },
        _sum: {
          points: true
        }
      });

      const userWithStats = {
        ...user,
        totalLoyaltyPoints: totalPoints._sum.points || 0,
        stats: {
          totalOrders: user._count.orders,
          totalReviews: user._count.reviews
        }
      };

      res.json({
        status: 'success',
        data: { user: userWithStats }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Update user profile
  // @route   PUT /api/users/profile
  // @access  Private
  async updateProfile(req, res, next) {
    try {
      const { firstName, lastName, phone, avatar } = req.body;

      const updatedUser = await prisma.user.update({
        where: { id: req.user.id },
        data: {
          firstName,
          lastName,
          phone,
          avatar,
          updatedAt: new Date()
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          avatar: true,
          role: true,
          status: true,
          isVerified: true,
          updatedAt: true
        }
      });

      logger.logBusiness('Profile updated', {
        userId: req.user.id,
        changes: { firstName, lastName, phone, avatar }
      });

      res.json({
        status: 'success',
        message: 'Profil mis à jour avec succès',
        data: { user: updatedUser }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Change password
  // @route   POST /api/users/change-password
  // @access  Private
  async changePassword(req, res, next) {
    try {
      const { currentPassword, newPassword } = req.body;

      // Get user with password
      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: { id: true, password: true }
      });

      if (!user) {
        return next(new AppError('Utilisateur non trouvé', 404, 'USER_NOT_FOUND'));
      }

      // Verify current password
      const isCurrentPasswordValid = await comparePassword(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        return next(new AppError('Mot de passe actuel incorrect', 400, 'INVALID_CURRENT_PASSWORD'));
      }

      // Hash new password
      const hashedNewPassword = await hashPassword(newPassword);

      // Update password
      await prisma.user.update({
        where: { id: req.user.id },
        data: { password: hashedNewPassword }
      });

      logger.logSecurity('Password changed', {
        userId: req.user.id,
        ip: req.ip
      });

      res.json({
        status: 'success',
        message: 'Mot de passe modifié avec succès'
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get user addresses
  // @route   GET /api/users/addresses
  // @access  Private
  async getAddresses(req, res, next) {
    try {
      const addresses = await prisma.address.findMany({
        where: { userId: req.user.id },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      res.json({
        status: 'success',
        data: { addresses }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Add new address
  // @route   POST /api/users/addresses
  // @access  Private
  async addAddress(req, res, next) {
    try {
      const {
        type = 'shipping',
        firstName,
        lastName,
        company,
        address1,
        address2,
        city,
        state,
        postalCode,
        country = 'TN',
        phone,
        isDefault = false
      } = req.body;

      // If this is set as default, unset other defaults
      if (isDefault) {
        await prisma.address.updateMany({
          where: { userId: req.user.id },
          data: { isDefault: false }
        });
      }

      const address = await prisma.address.create({
        data: {
          userId: req.user.id,
          type,
          firstName,
          lastName,
          company,
          address1,
          address2,
          city,
          state,
          postalCode,
          country,
          phone,
          isDefault
        }
      });

      logger.logBusiness('Address added', {
        userId: req.user.id,
        addressId: address.id,
        type,
        city
      });

      res.status(201).json({
        status: 'success',
        message: 'Adresse ajoutée avec succès',
        data: { address }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Update address
  // @route   PUT /api/users/addresses/:id
  // @access  Private
  async updateAddress(req, res, next) {
    try {
      const { id } = req.params;
      const updateData = { ...req.body };

      // Verify address belongs to user
      const existingAddress = await prisma.address.findFirst({
        where: { id, userId: req.user.id }
      });

      if (!existingAddress) {
        return next(new AppError('Adresse non trouvée', 404, 'ADDRESS_NOT_FOUND'));
      }

      // If this is set as default, unset other defaults
      if (updateData.isDefault) {
        await prisma.address.updateMany({
          where: { 
            userId: req.user.id,
            id: { not: id }
          },
          data: { isDefault: false }
        });
      }

      const address = await prisma.address.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      });

      logger.logBusiness('Address updated', {
        userId: req.user.id,
        addressId: address.id,
        changes: updateData
      });

      res.json({
        status: 'success',
        message: 'Adresse mise à jour avec succès',
        data: { address }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Delete address
  // @route   DELETE /api/users/addresses/:id
  // @access  Private
  async deleteAddress(req, res, next) {
    try {
      const { id } = req.params;

      // Verify address belongs to user
      const address = await prisma.address.findFirst({
        where: { id, userId: req.user.id }
      });

      if (!address) {
        return next(new AppError('Adresse non trouvée', 404, 'ADDRESS_NOT_FOUND'));
      }

      // Check if address is used in orders
      const orderCount = await prisma.order.count({
        where: { addressId: id }
      });

      if (orderCount > 0) {
        return next(new AppError('Impossible de supprimer une adresse utilisée dans des commandes', 400, 'ADDRESS_IN_USE'));
      }

      await prisma.address.delete({
        where: { id }
      });

      // If deleted address was default, set another as default
      if (address.isDefault) {
        const firstAddress = await prisma.address.findFirst({
          where: { userId: req.user.id },
          orderBy: { createdAt: 'asc' }
        });

        if (firstAddress) {
          await prisma.address.update({
            where: { id: firstAddress.id },
            data: { isDefault: true }
          });
        }
      }

      logger.logBusiness('Address deleted', {
        userId: req.user.id,
        addressId: id
      });

      res.json({
        status: 'success',
        message: 'Adresse supprimée avec succès'
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get loyalty points history
  // @route   GET /api/users/loyalty-points
  // @access  Private
  async getLoyaltyPoints(req, res, next) {
    try {
      const { page = 1, limit = 20 } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

      const [points, total, summary] = await Promise.all([
        prisma.loyaltyPoint.findMany({
          where: { userId: req.user.id },
          skip,
          take,
          orderBy: { createdAt: 'desc' },
          include: {
            order: {
              select: { orderNumber: true }
            }
          }
        }),
        prisma.loyaltyPoint.count({
          where: { userId: req.user.id }
        }),
        prisma.loyaltyPoint.groupBy({
          by: ['type'],
          where: { userId: req.user.id },
          _sum: {
            points: true
          }
        })
      ]);

      const totalPages = Math.ceil(total / take);

      // Calculate balance
      const earned = summary.find(s => s.type === 'earned')?._sum.points || 0;
      const redeemed = summary.find(s => s.type === 'redeemed')?._sum.points || 0;
      const balance = earned - redeemed;

      res.json({
        status: 'success',
        data: {
          points,
          summary: {
            balance,
            earned,
            redeemed
          },
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalItems: total,
            itemsPerPage: take
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get user orders
  // @route   GET /api/users/orders
  // @access  Private
  async getOrders(req, res, next) {
    try {
      const { page = 1, limit = 10, status } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

      const where = {
        userId: req.user.id,
        ...(status && { status })
      };

      const [orders, total] = await Promise.all([
        prisma.order.findMany({
          where,
          skip,
          take,
          orderBy: { createdAt: 'desc' },
          include: {
            items: {
              include: {
                variant: {
                  include: {
                    product: {
                      select: {
                        id: true,
                        name: true,
                        slug: true,
                        images: {
                          select: { url: true },
                          take: 1,
                          orderBy: { sortOrder: 'asc' }
                        }
                      }
                    }
                  }
                }
              }
            },
            payment: {
              select: {
                method: true,
                status: true,
                paidAt: true
              }
            },
            shipping: {
              select: {
                method: true,
                trackingNumber: true,
                estimatedDelivery: true,
                shippedAt: true,
                deliveredAt: true
              }
            }
          }
        }),
        prisma.order.count({ where })
      ]);

      const totalPages = Math.ceil(total / take);

      res.json({
        status: 'success',
        data: {
          orders,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalItems: total,
            itemsPerPage: take
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }
}

module.exports = new UserController();

const { PrismaClient } = require('@prisma/client');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

class VariantController {
  // @desc    Get all variants for a product
  // @route   GET /api/products/:id/variants
  // @access  Public
  async getProductVariants(req, res, next) {
    try {
      const { id } = req.params;

      const variants = await prisma.productVariant.findMany({
        where: { productId: id },
        orderBy: [
          { isDefault: 'desc' },
          { createdAt: 'asc' }
        ]
      });

      res.json({
        status: 'success',
        data: { variants }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Create new variant for a product
  // @route   POST /api/products/:id/variants
  // @access  Private (Admin/Employee)
  async createVariant(req, res, next) {
    try {
      const { id: productId } = req.params;
      const {
        size,
        color,
        material,
        price,
        comparePrice,
        costPrice,
        stock = 0,
        weight,
        isDefault = false
      } = req.body;

      // Check if product exists
      const product = await prisma.product.findUnique({
        where: { id: productId },
        select: { id: true, sku: true, name: true }
      });

      if (!product) {
        return next(new AppError('Produit non trouvé', 404, 'PRODUCT_NOT_FOUND'));
      }

      // Generate variant SKU
      const variantCount = await prisma.productVariant.count({
        where: { productId }
      });
      const sku = `${product.sku}-${variantCount + 1}`;

      // If this is set as default, unset other defaults
      if (isDefault) {
        await prisma.productVariant.updateMany({
          where: { productId },
          data: { isDefault: false }
        });
      }

      // Create variant
      const variant = await prisma.productVariant.create({
        data: {
          productId,
          sku,
          size,
          color,
          material,
          price,
          comparePrice,
          costPrice,
          stock,
          weight,
          isDefault
        }
      });

      // Log stock movement if stock > 0
      if (stock > 0) {
        await prisma.stockMovement.create({
          data: {
            productId,
            variantId: variant.id,
            type: 'in',
            quantity: stock,
            reason: 'Initial stock',
            reference: `variant_${variant.id}`
          }
        });
      }

      logger.logBusiness('Product variant created', {
        productId,
        variantId: variant.id,
        sku: variant.sku,
        userId: req.user.id
      });

      res.status(201).json({
        status: 'success',
        message: 'Variante créée avec succès',
        data: { variant }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Update variant
  // @route   PUT /api/products/:id/variants/:variantId
  // @access  Private (Admin/Employee)
  async updateVariant(req, res, next) {
    try {
      const { id: productId, variantId } = req.params;
      const updateData = { ...req.body };

      // Get current variant
      const currentVariant = await prisma.productVariant.findUnique({
        where: { id: variantId },
        select: { stock: true, price: true }
      });

      if (!currentVariant) {
        return next(new AppError('Variante non trouvée', 404, 'VARIANT_NOT_FOUND'));
      }

      // If this is set as default, unset other defaults
      if (updateData.isDefault) {
        await prisma.productVariant.updateMany({
          where: { 
            productId,
            id: { not: variantId }
          },
          data: { isDefault: false }
        });
      }

      // Handle stock changes
      if (updateData.stock !== undefined && updateData.stock !== currentVariant.stock) {
        const stockDifference = updateData.stock - currentVariant.stock;
        
        // Log stock movement
        await prisma.stockMovement.create({
          data: {
            productId,
            variantId,
            type: stockDifference > 0 ? 'in' : 'out',
            quantity: Math.abs(stockDifference),
            reason: 'Manual adjustment',
            reference: `adjustment_${Date.now()}`,
            userId: req.user.id
          }
        });
      }

      // Update variant
      const variant = await prisma.productVariant.update({
        where: { id: variantId },
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      });

      logger.logBusiness('Product variant updated', {
        productId,
        variantId: variant.id,
        changes: updateData,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: 'Variante mise à jour avec succès',
        data: { variant }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Delete variant
  // @route   DELETE /api/products/:id/variants/:variantId
  // @access  Private (Admin/Employee)
  async deleteVariant(req, res, next) {
    try {
      const { variantId } = req.params;

      // Check if variant exists
      const variant = await prisma.productVariant.findUnique({
        where: { id: variantId },
        select: { 
          id: true, 
          sku: true, 
          productId: true,
          isDefault: true
        }
      });

      if (!variant) {
        return next(new AppError('Variante non trouvée', 404, 'VARIANT_NOT_FOUND'));
      }

      // Check if this is the only variant
      const variantCount = await prisma.productVariant.count({
        where: { productId: variant.productId }
      });

      if (variantCount === 1) {
        return next(new AppError('Impossible de supprimer la dernière variante', 400, 'LAST_VARIANT'));
      }

      // Check if variant is used in orders
      const orderItemCount = await prisma.orderItem.count({
        where: { variantId }
      });

      if (orderItemCount > 0) {
        return next(new AppError('Impossible de supprimer une variante utilisée dans des commandes', 400, 'VARIANT_IN_USE'));
      }

      // Delete variant
      await prisma.productVariant.delete({
        where: { id: variantId }
      });

      // If deleted variant was default, set another as default
      if (variant.isDefault) {
        const firstVariant = await prisma.productVariant.findFirst({
          where: { productId: variant.productId },
          orderBy: { createdAt: 'asc' }
        });

        if (firstVariant) {
          await prisma.productVariant.update({
            where: { id: firstVariant.id },
            data: { isDefault: true }
          });
        }
      }

      logger.logBusiness('Product variant deleted', {
        productId: variant.productId,
        variantId: variant.id,
        sku: variant.sku,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: 'Variante supprimée avec succès'
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Update variant stock
  // @route   PATCH /api/products/:id/variants/:variantId/stock
  // @access  Private (Admin/Employee)
  async updateStock(req, res, next) {
    try {
      const { id: productId, variantId } = req.params;
      const { quantity, type, reason } = req.body;

      if (!['in', 'out', 'adjustment'].includes(type)) {
        return next(new AppError('Type de mouvement invalide', 400, 'INVALID_MOVEMENT_TYPE'));
      }

      if (!quantity || quantity <= 0) {
        return next(new AppError('Quantité invalide', 400, 'INVALID_QUANTITY'));
      }

      // Get current variant
      const variant = await prisma.productVariant.findUnique({
        where: { id: variantId },
        select: { stock: true }
      });

      if (!variant) {
        return next(new AppError('Variante non trouvée', 404, 'VARIANT_NOT_FOUND'));
      }

      let newStock;
      switch (type) {
        case 'in':
          newStock = variant.stock + quantity;
          break;
        case 'out':
          newStock = Math.max(0, variant.stock - quantity);
          break;
        case 'adjustment':
          newStock = quantity;
          break;
      }

      // Update stock
      const updatedVariant = await prisma.productVariant.update({
        where: { id: variantId },
        data: { stock: newStock }
      });

      // Log stock movement
      await prisma.stockMovement.create({
        data: {
          productId,
          variantId,
          type,
          quantity: type === 'adjustment' ? Math.abs(quantity - variant.stock) : quantity,
          reason: reason || `Stock ${type}`,
          reference: `manual_${Date.now()}`,
          userId: req.user.id
        }
      });

      logger.logBusiness('Stock updated', {
        productId,
        variantId,
        oldStock: variant.stock,
        newStock,
        type,
        quantity,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: 'Stock mis à jour avec succès',
        data: { 
          variant: updatedVariant,
          oldStock: variant.stock,
          newStock
        }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get stock movements for a variant
  // @route   GET /api/products/:id/variants/:variantId/movements
  // @access  Private (Admin/Employee)
  async getStockMovements(req, res, next) {
    try {
      const { variantId } = req.params;
      const { page = 1, limit = 20 } = req.query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

      const [movements, total] = await Promise.all([
        prisma.stockMovement.findMany({
          where: { variantId },
          skip,
          take,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.stockMovement.count({
          where: { variantId }
        })
      ]);

      const totalPages = Math.ceil(total / take);

      res.json({
        status: 'success',
        data: {
          movements,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalItems: total,
            itemsPerPage: take
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }
}

module.exports = new VariantController();

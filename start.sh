#!/bin/bash

# 🚀 Script de démarrage rapide SassouFashion
# Ce script configure et démarre l'application complète

set -e

echo "🛍️  SassouFashion - Démarrage de l'application"
echo "=============================================="

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier les prérequis
check_prerequisites() {
    log_info "Vérification des prérequis..."
    
    # Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé. Veuillez installer Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js version 18+ requis. Version actuelle: $(node -v)"
        exit 1
    fi
    
    # npm
    if ! command -v npm &> /dev/null; then
        log_error "npm n'est pas installé"
        exit 1
    fi
    
    # PostgreSQL (optionnel avec Docker)
    if ! command -v psql &> /dev/null && ! command -v docker &> /dev/null; then
        log_warning "PostgreSQL ou Docker requis pour la base de données"
    fi
    
    log_success "Prérequis vérifiés"
}

# Configuration de l'environnement
setup_environment() {
    log_info "Configuration de l'environnement..."
    
    # Backend
    if [ ! -f "backend/.env" ]; then
        log_info "Création du fichier .env backend..."
        cp backend/.env.example backend/.env
        log_warning "Veuillez configurer backend/.env avec vos paramètres"
    fi
    
    # Frontend
    if [ ! -f "frontend/.env.local" ]; then
        log_info "Création du fichier .env.local frontend..."
        cp frontend/.env.example frontend/.env.local
    fi
    
    log_success "Environnement configuré"
}

# Installation des dépendances
install_dependencies() {
    log_info "Installation des dépendances..."
    
    # Backend
    log_info "Installation des dépendances backend..."
    cd backend
    npm install
    cd ..
    
    # Frontend
    log_info "Installation des dépendances frontend..."
    cd frontend
    npm install
    cd ..
    
    log_success "Dépendances installées"
}

# Configuration de la base de données
setup_database() {
    log_info "Configuration de la base de données..."
    
    cd backend
    
    # Générer le client Prisma
    log_info "Génération du client Prisma..."
    npx prisma generate
    
    # Exécuter les migrations
    log_info "Exécution des migrations..."
    npx prisma migrate dev --name init
    
    # Peupler la base de données (optionnel)
    if [ -f "prisma/seed.js" ]; then
        log_info "Peuplement de la base de données..."
        npm run db:seed
    fi
    
    cd ..
    log_success "Base de données configurée"
}

# Démarrage des services
start_services() {
    log_info "Démarrage des services..."
    
    # Démarrer le backend en arrière-plan
    log_info "Démarrage du backend (port 5000)..."
    cd backend
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    # Attendre que le backend soit prêt
    sleep 5
    
    # Démarrer le frontend
    log_info "Démarrage du frontend (port 3000)..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    log_success "Services démarrés"
    echo ""
    echo "🎉 Application prête !"
    echo "📱 Frontend: http://localhost:3000"
    echo "🔧 Backend API: http://localhost:5000"
    echo "📊 Prisma Studio: npx prisma studio (dans le dossier backend)"
    echo ""
    echo "Pour arrêter l'application, appuyez sur Ctrl+C"
    
    # Attendre l'interruption
    trap 'kill $BACKEND_PID $FRONTEND_PID; exit' INT
    wait
}

# Fonction principale
main() {
    case "${1:-start}" in
        "check")
            check_prerequisites
            ;;
        "setup")
            check_prerequisites
            setup_environment
            install_dependencies
            setup_database
            ;;
        "start")
            check_prerequisites
            setup_environment
            install_dependencies
            setup_database
            start_services
            ;;
        "docker")
            log_info "Démarrage avec Docker..."
            docker-compose up -d
            log_success "Application démarrée avec Docker"
            echo "📱 Application: http://localhost:3000"
            ;;
        "stop")
            log_info "Arrêt des services Docker..."
            docker-compose down
            log_success "Services arrêtés"
            ;;
        *)
            echo "Usage: $0 {check|setup|start|docker|stop}"
            echo ""
            echo "  check  - Vérifier les prérequis"
            echo "  setup  - Configurer l'environnement et installer les dépendances"
            echo "  start  - Démarrer l'application complète (défaut)"
            echo "  docker - Démarrer avec Docker"
            echo "  stop   - Arrêter les services Docker"
            exit 1
            ;;
    esac
}

# Exécuter la fonction principale
main "$@"

import { apiClient, buildQueryString } from './api';
import type { ApiResponse, PaginatedResponse } from '@/types';

// Types for promotions
export interface Promotion {
  id: string;
  name: string;
  description?: string;
  type: 'PERCENTAGE' | 'FIXED_AMOUNT';
  value: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  products?: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  categories?: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePromotionRequest {
  name: string;
  description?: string;
  type: 'PERCENTAGE' | 'FIXED_AMOUNT';
  value: number;
  startDate: string;
  endDate: string;
  isActive?: boolean;
}

export interface PromotionFilters {
  page?: number;
  limit?: number;
  active?: boolean;
}

export interface PromotionStats {
  promotion: Promotion;
  stats: {
    totalSales: number;
    totalRevenue: number;
    orderCount: number;
    affectedProducts: number;
  };
}

// Promotion service
export const promotionService = {
  // Get all promotions (Admin)
  getPromotions: async (filters: PromotionFilters = {}): Promise<PaginatedResponse<Promotion>> => {
    const queryString = buildQueryString(filters);
    const url = queryString ? `/promotions?${queryString}` : '/promotions';
    
    const response = await apiClient.get<ApiResponse<PaginatedResponse<Promotion>>>(url);
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Failed to get promotions');
  },

  // Get active promotions (Public)
  getActivePromotions: async (): Promise<Promotion[]> => {
    const response = await apiClient.get<ApiResponse<{ promotions: Promotion[] }>>('/promotions/active');
    
    if (response.data?.promotions) {
      return response.data.promotions;
    }
    
    return [];
  },

  // Get single promotion
  getPromotion: async (promotionId: string): Promise<Promotion> => {
    const response = await apiClient.get<ApiResponse<{ promotion: Promotion }>>(`/promotions/${promotionId}`);
    
    if (response.data?.promotion) {
      return response.data.promotion;
    }
    
    throw new Error('Promotion not found');
  },

  // Create promotion for product
  createProductPromotion: async (productId: string, promotionData: CreatePromotionRequest): Promise<Promotion> => {
    const response = await apiClient.post<ApiResponse<{ promotion: Promotion }>>(
      `/promotions/product/${productId}`,
      promotionData
    );
    
    if (response.data?.promotion) {
      return response.data.promotion;
    }
    
    throw new Error('Failed to create promotion');
  },

  // Create promotion for category
  createCategoryPromotion: async (categoryId: string, promotionData: CreatePromotionRequest): Promise<Promotion> => {
    const response = await apiClient.post<ApiResponse<{ promotion: Promotion }>>(
      `/promotions/category/${categoryId}`,
      promotionData
    );
    
    if (response.data?.promotion) {
      return response.data.promotion;
    }
    
    throw new Error('Failed to create promotion');
  },

  // Toggle promotion status
  togglePromotion: async (promotionId: string, isActive: boolean): Promise<Promotion> => {
    const response = await apiClient.patch<ApiResponse<{ promotion: Promotion }>>(
      `/promotions/${promotionId}/toggle`,
      { isActive }
    );
    
    if (response.data?.promotion) {
      return response.data.promotion;
    }
    
    throw new Error('Failed to toggle promotion');
  },

  // Get promotion statistics
  getPromotionStats: async (promotionId: string): Promise<PromotionStats> => {
    const response = await apiClient.get<ApiResponse<PromotionStats>>(`/promotions/${promotionId}/stats`);
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Failed to get promotion stats');
  },

  // Delete promotion
  deletePromotion: async (promotionId: string): Promise<void> => {
    await apiClient.delete<ApiResponse>(`/promotions/${promotionId}`);
  },

  // Check promotion schedules (Admin)
  checkPromotionSchedules: async (): Promise<{ activated: number; deactivated: number }> => {
    const response = await apiClient.post<ApiResponse<{ activated: number; deactivated: number }>>(
      '/promotions/check-schedules'
    );
    
    if (response.data) {
      return response.data;
    }
    
    throw new Error('Failed to check promotion schedules');
  }
};

// Promotion utilities
export const promotionUtils = {
  // Format promotion value for display
  formatPromotionValue: (promotion: Promotion): string => {
    if (promotion.type === 'PERCENTAGE') {
      return `${promotion.value}%`;
    } else {
      return `${promotion.value} TND`;
    }
  },

  // Calculate discounted price
  calculateDiscountedPrice: (originalPrice: number, promotion: Promotion): number => {
    if (promotion.type === 'PERCENTAGE') {
      return originalPrice * (1 - promotion.value / 100);
    } else {
      return Math.max(0, originalPrice - promotion.value);
    }
  },

  // Check if promotion is currently active
  isPromotionActive: (promotion: Promotion): boolean => {
    if (!promotion.isActive) return false;
    
    const now = new Date();
    const startDate = new Date(promotion.startDate);
    const endDate = new Date(promotion.endDate);
    
    return now >= startDate && now <= endDate;
  },

  // Get promotion status
  getPromotionStatus: (promotion: Promotion): 'active' | 'scheduled' | 'expired' | 'inactive' => {
    if (!promotion.isActive) return 'inactive';
    
    const now = new Date();
    const startDate = new Date(promotion.startDate);
    const endDate = new Date(promotion.endDate);
    
    if (now < startDate) return 'scheduled';
    if (now > endDate) return 'expired';
    return 'active';
  },

  // Get promotion status color
  getPromotionStatusColor: (promotion: Promotion): string => {
    const status = promotionUtils.getPromotionStatus(promotion);
    
    const colors = {
      active: 'success',
      scheduled: 'warning',
      expired: 'error',
      inactive: 'secondary'
    };
    
    return colors[status];
  },

  // Get promotion status label
  getPromotionStatusLabel: (promotion: Promotion): string => {
    const status = promotionUtils.getPromotionStatus(promotion);
    
    const labels = {
      active: 'Active',
      scheduled: 'Programmée',
      expired: 'Expirée',
      inactive: 'Inactive'
    };
    
    return labels[status];
  },

  // Format date range
  formatDateRange: (startDate: string, endDate: string): string => {
    const start = new Date(startDate).toLocaleDateString('fr-FR');
    const end = new Date(endDate).toLocaleDateString('fr-FR');
    
    return `${start} - ${end}`;
  },

  // Calculate promotion duration
  getPromotionDuration: (startDate: string, endDate: string): number => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  },

  // Validate promotion data
  validatePromotion: (promotionData: Partial<CreatePromotionRequest>) => {
    const errors: string[] = [];
    
    if (!promotionData.name?.trim()) {
      errors.push('Nom de la promotion requis');
    }
    
    if (!promotionData.type) {
      errors.push('Type de promotion requis');
    }
    
    if (!promotionData.value || promotionData.value <= 0) {
      errors.push('Valeur de promotion invalide');
    }
    
    if (promotionData.type === 'PERCENTAGE' && promotionData.value > 100) {
      errors.push('Le pourcentage ne peut pas dépasser 100%');
    }
    
    if (!promotionData.startDate) {
      errors.push('Date de début requise');
    }
    
    if (!promotionData.endDate) {
      errors.push('Date de fin requise');
    }
    
    if (promotionData.startDate && promotionData.endDate) {
      const startDate = new Date(promotionData.startDate);
      const endDate = new Date(promotionData.endDate);
      
      if (endDate <= startDate) {
        errors.push('La date de fin doit être après la date de début');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Get promotion type options
  getPromotionTypeOptions: () => [
    { value: 'PERCENTAGE', label: 'Pourcentage' },
    { value: 'FIXED_AMOUNT', label: 'Montant fixe' }
  ]
};

export default promotionService;

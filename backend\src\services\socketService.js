const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');
const config = require('../config/config');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId
    this.adminSockets = new Set(); // Admin socket IDs
  }

  init(io) {
    this.io = io;
    this.setupSocketHandlers();
    logger.info('Socket.io service initialized');
  }

  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      logger.info('New socket connection', { socketId: socket.id });

      // Authentication
      socket.on('authenticate', async (token) => {
        try {
          const decoded = jwt.verify(token, config.jwtSecret);
          const user = await prisma.user.findUnique({
            where: { id: decoded.id },
            select: { id: true, role: true, status: true }
          });

          if (user && user.status === 'ACTIVE') {
            socket.userId = user.id;
            socket.userRole = user.role;
            
            // Store user connection
            this.connectedUsers.set(user.id, socket.id);
            
            // Add to admin sockets if admin
            if (user.role === 'ADMIN') {
              this.adminSockets.add(socket.id);
            }

            socket.emit('authenticated', { userId: user.id, role: user.role });
            
            logger.info('Socket authenticated', {
              socketId: socket.id,
              userId: user.id,
              role: user.role
            });

            // Send pending notifications
            await this.sendPendingNotifications(user.id, socket);
          } else {
            socket.emit('authentication_error', 'Invalid user');
            socket.disconnect();
          }
        } catch (error) {
          socket.emit('authentication_error', 'Invalid token');
          socket.disconnect();
        }
      });

      // Join user-specific room
      socket.on('join_user_room', (userId) => {
        if (socket.userId === userId || socket.userRole === 'ADMIN') {
          socket.join(`user_${userId}`);
          logger.info('User joined room', { socketId: socket.id, userId });
        }
      });

      // Join admin room
      socket.on('join_admin_room', () => {
        if (socket.userRole === 'ADMIN') {
          socket.join('admin_room');
          logger.info('Admin joined admin room', { socketId: socket.id });
        }
      });

      // Handle order status updates (admin only)
      socket.on('update_order_status', async (data) => {
        if (socket.userRole !== 'ADMIN') {
          socket.emit('error', 'Unauthorized');
          return;
        }

        try {
          const { orderId, status } = data;
          
          // Update order in database
          const order = await prisma.order.update({
            where: { id: orderId },
            data: { status },
            include: {
              user: { select: { id: true, firstName: true, email: true } }
            }
          });

          // Notify customer
          this.notifyUser(order.user.id, 'order_status_updated', {
            orderId: order.id,
            orderNumber: order.orderNumber,
            status: order.status,
            message: this.getOrderStatusMessage(order.status)
          });

          // Notify all admins
          this.notifyAdmins('order_updated', {
            orderId: order.id,
            orderNumber: order.orderNumber,
            status: order.status,
            customerName: order.user.firstName
          });

          logger.logBusiness('Order status updated via socket', {
            orderId: order.id,
            newStatus: status,
            adminId: socket.userId
          });

        } catch (error) {
          socket.emit('error', 'Failed to update order status');
          logger.error('Socket order update error', error);
        }
      });

      // Handle support messages
      socket.on('send_support_message', async (data) => {
        try {
          const { ticketId, message } = data;
          
          // Verify user can access this ticket
          const ticket = await prisma.supportTicket.findFirst({
            where: {
              id: ticketId,
              OR: [
                { userId: socket.userId },
                { assignedTo: socket.userId }
              ]
            },
            include: { user: true }
          });

          if (!ticket && socket.userRole !== 'ADMIN') {
            socket.emit('error', 'Ticket not found');
            return;
          }

          // Create message
          const newMessage = await prisma.message.create({
            data: {
              ticketId,
              userId: socket.userId,
              content: message,
              isInternal: socket.userRole !== 'CLIENT'
            }
          });

          // Notify relevant parties
          if (socket.userRole === 'CLIENT') {
            // Notify admins
            this.notifyAdmins('new_support_message', {
              ticketId,
              message: newMessage,
              customerName: ticket.user.firstName
            });
          } else {
            // Notify customer
            this.notifyUser(ticket.userId, 'support_message_reply', {
              ticketId,
              message: newMessage
            });
          }

        } catch (error) {
          socket.emit('error', 'Failed to send message');
          logger.error('Socket support message error', error);
        }
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        if (socket.userId) {
          this.connectedUsers.delete(socket.userId);
        }
        
        if (this.adminSockets.has(socket.id)) {
          this.adminSockets.delete(socket.id);
        }

        logger.info('Socket disconnected', {
          socketId: socket.id,
          userId: socket.userId
        });
      });
    });
  }

  // Send notification to specific user
  notifyUser(userId, event, data) {
    const socketId = this.connectedUsers.get(userId);
    if (socketId) {
      this.io.to(socketId).emit(event, data);
      logger.info('Notification sent to user', { userId, event });
    } else {
      // Store notification for later delivery
      this.storeNotification(userId, event, data);
    }
  }

  // Send notification to all admins
  notifyAdmins(event, data) {
    this.io.to('admin_room').emit(event, data);
    logger.info('Notification sent to admins', { event });
  }

  // Send notification to all connected users
  broadcast(event, data) {
    this.io.emit(event, data);
    logger.info('Broadcast notification sent', { event });
  }

  // Store notification in database for offline users
  async storeNotification(userId, type, data) {
    try {
      await prisma.notification.create({
        data: {
          userId,
          type: type.toUpperCase(),
          title: this.getNotificationTitle(type),
          message: this.getNotificationMessage(type, data),
          data: data
        }
      });
    } catch (error) {
      logger.error('Failed to store notification', error);
    }
  }

  // Send pending notifications to newly connected user
  async sendPendingNotifications(userId, socket) {
    try {
      const notifications = await prisma.notification.findMany({
        where: {
          userId,
          isRead: false,
          sentAt: null
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      });

      for (const notification of notifications) {
        socket.emit('notification', {
          id: notification.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          createdAt: notification.createdAt
        });

        // Mark as sent
        await prisma.notification.update({
          where: { id: notification.id },
          data: { sentAt: new Date() }
        });
      }

      if (notifications.length > 0) {
        logger.info('Sent pending notifications', {
          userId,
          count: notifications.length
        });
      }
    } catch (error) {
      logger.error('Failed to send pending notifications', error);
    }
  }

  // Helper methods for messages
  getOrderStatusMessage(status) {
    const messages = {
      PENDING: 'Votre commande est en attente de confirmation',
      CONFIRMED: 'Votre commande a été confirmée',
      PROCESSING: 'Votre commande est en cours de préparation',
      SHIPPED: 'Votre commande a été expédiée',
      DELIVERED: 'Votre commande a été livrée',
      CANCELLED: 'Votre commande a été annulée',
      REFUNDED: 'Votre commande a été remboursée'
    };
    return messages[status] || 'Statut de commande mis à jour';
  }

  getNotificationTitle(type) {
    const titles = {
      order_status_updated: 'Commande mise à jour',
      new_support_message: 'Nouveau message support',
      support_message_reply: 'Réponse support',
      stock_alert: 'Alerte stock',
      promotion: 'Nouvelle promotion'
    };
    return titles[type] || 'Notification';
  }

  getNotificationMessage(type, data) {
    switch (type) {
      case 'order_status_updated':
        return `Commande #${data.orderNumber} : ${data.message}`;
      case 'new_support_message':
        return `Nouveau message de ${data.customerName}`;
      case 'support_message_reply':
        return 'Vous avez reçu une réponse à votre ticket';
      case 'stock_alert':
        return `Stock faible pour ${data.productName}`;
      case 'promotion':
        return `Nouvelle promotion : ${data.promotionName}`;
      default:
        return 'Nouvelle notification';
    }
  }

  // Public methods for external use
  sendOrderNotification(userId, orderId, status) {
    this.notifyUser(userId, 'order_status_updated', {
      orderId,
      status,
      message: this.getOrderStatusMessage(status)
    });
  }

  sendStockAlert(productName, currentStock) {
    this.notifyAdmins('stock_alert', {
      productName,
      currentStock,
      threshold: config.business.lowStockThreshold
    });
  }

  sendNewOrderAlert(order) {
    this.notifyAdmins('new_order', {
      orderId: order.id,
      orderNumber: order.orderNumber,
      customerName: order.user.firstName,
      total: order.total
    });
  }
}

module.exports = new SocketService();

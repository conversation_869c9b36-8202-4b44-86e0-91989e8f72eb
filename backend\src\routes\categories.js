const express = require('express');
const { body, param } = require('express-validator');
const { authenticate, authorize } = require('../middleware/auth');
const categoryController = require('../controllers/categoryController');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Validation middleware
const validateCategory = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Le nom doit contenir entre 2 et 100 caractères'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('La description ne peut pas dépasser 500 caractères'),
  body('parentId')
    .optional()
    .isUUID()
    .withMessage('ID parent invalide'),
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Ordre de tri invalide')
];

const validateId = [
  param('id')
    .custom((value) => {
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value);
      const isSlug = /^[a-z0-9-]+$/.test(value);
      
      if (!isUUID && !isSlug) {
        throw new Error('ID ou slug invalide');
      }
      return true;
    })
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    return next(new AppError(errorMessages.join(', '), 400, 'VALIDATION_ERROR'));
  }
  next();
};

// Public routes
router.get('/', categoryController.getCategories);
router.get('/:identifier', validateId, handleValidationErrors, categoryController.getCategory);
router.get('/:identifier/products', validateId, handleValidationErrors, categoryController.getCategoryProducts);

// Protected routes (Admin/Employee)
router.post('/', 
  authenticate,
  authorize('ADMIN', 'EMPLOYEE'),
  validateCategory,
  handleValidationErrors,
  categoryController.createCategory
);

router.put('/:id', 
  authenticate,
  authorize('ADMIN', 'EMPLOYEE'),
  validateId,
  validateCategory,
  handleValidationErrors,
  categoryController.updateCategory
);

router.delete('/:id', 
  authenticate,
  authorize('ADMIN'),
  validateId,
  handleValidationErrors,
  categoryController.deleteCategory
);

module.exports = router;

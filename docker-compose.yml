version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sassoufashion_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: sassoufashion
      POSTGRES_USER: sassoufashion_user
      POSTGRES_PASSWORD: sassoufashion_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - sassoufashion_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: sassoufashion_redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - sassoufashion_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: sassoufashion_api
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************************************/sassoufashion
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      PORT: 5000
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    networks:
      - sassoufashion_network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: sassoufashion_web
    restart: unless-stopped
    environment:
      VITE_API_URL: http://localhost:5000/api
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - sassoufashion_network

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: sassoufashion_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - frontend
      - backend
    networks:
      - sassoufashion_network
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  sassoufashion_network:
    driver: bridge

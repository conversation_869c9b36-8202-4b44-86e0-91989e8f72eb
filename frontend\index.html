<!doctype html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- SEO Meta Tags -->
    <title>SassouFashion - Boutique de Mode en Ligne | Vêtements Tendance</title>
    <meta name="description" content="Découvrez SassouFashion, votre boutique de mode en ligne. Collection exclusive de vêtements pour homme, femme et enfant. Livraison rapide en Tunisie." />
    <meta name="keywords" content="mode, vêtements, fashion, boutique en ligne, Tunisie, homme, femme, enfant, accessoires" />
    <meta name="author" content="SassouFashion" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="SassouFashion - Boutique de Mode en Ligne" />
    <meta property="og:description" content="Découvrez notre collection exclusive de vêtements tendance pour toute la famille." />
    <meta property="og:image" content="/og-image.jpg" />
    <meta property="og:url" content="https://sassoufashion.com" />
    <meta property="og:type" content="website" />
    <meta property="og:locale" content="fr_FR" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="SassouFashion - Boutique de Mode en Ligne" />
    <meta name="twitter:description" content="Découvrez notre collection exclusive de vêtements tendance pour toute la famille." />
    <meta name="twitter:image" content="/twitter-image.jpg" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#2563eb" />
    <meta name="msapplication-TileColor" content="#2563eb" />
    <meta name="theme-color" content="#2563eb" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://res.cloudinary.com" />
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ClothingStore",
      "name": "SassouFashion",
      "description": "Boutique de mode en ligne spécialisée dans les vêtements tendance pour homme, femme et enfant",
      "url": "https://sassoufashion.com",
      "logo": "https://sassoufashion.com/logo.png",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "TN",
        "addressLocality": "Tunis"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+216-XX-XXX-XXX",
        "contactType": "customer service",
        "availableLanguage": ["French", "Arabic"]
      },
      "sameAs": [
        "https://www.facebook.com/sassoufashion",
        "https://www.instagram.com/sassoufashion"
      ]
    }
    </script>
    
    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//res.cloudinary.com" />
    
    <!-- Critical CSS will be inlined here by build process -->
  </head>
  <body>
    <div id="root"></div>
    
    <!-- Loading fallback -->
    <noscript>
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: system-ui, sans-serif;
        text-align: center;
        z-index: 9999;
      ">
        <div>
          <h1 style="color: #2563eb; margin-bottom: 1rem;">SassouFashion</h1>
          <p style="color: #6b7280;">JavaScript est requis pour utiliser cette application.</p>
          <p style="color: #6b7280; margin-top: 0.5rem;">Veuillez activer JavaScript dans votre navigateur.</p>
        </div>
      </div>
    </noscript>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>

const { PrismaClient } = require('@prisma/client');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const slugify = require('slugify');

const prisma = new PrismaClient();

class CategoryController {
  // @desc    Get all categories with hierarchy
  // @route   GET /api/categories
  // @access  Public
  async getCategories(req, res, next) {
    try {
      const { includeInactive = false } = req.query;

      const where = {
        ...(includeInactive !== 'true' && { isActive: true })
      };

      // Get all categories
      const categories = await prisma.category.findMany({
        where,
        orderBy: [
          { sortOrder: 'asc' },
          { name: 'asc' }
        ],
        include: {
          children: {
            where,
            orderBy: [
              { sortOrder: 'asc' },
              { name: 'asc' }
            ],
            include: {
              _count: {
                select: { products: true }
              }
            }
          },
          _count: {
            select: { products: true }
          }
        }
      });

      // Build hierarchy (only root categories)
      const rootCategories = categories.filter(cat => !cat.parentId);

      // Add product counts recursively
      const categoriesWithCounts = rootCategories.map(category => ({
        ...category,
        productCount: category._count.products + 
          category.children.reduce((sum, child) => sum + child._count.products, 0)
      }));

      res.json({
        status: 'success',
        data: { categories: categoriesWithCounts }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get single category by ID or slug
  // @route   GET /api/categories/:identifier
  // @access  Public
  async getCategory(req, res, next) {
    try {
      const { identifier } = req.params;
      
      const isId = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(identifier);
      const where = isId ? { id: identifier } : { slug: identifier };

      const category = await prisma.category.findUnique({
        where,
        include: {
          parent: {
            select: { id: true, name: true, slug: true }
          },
          children: {
            where: { isActive: true },
            orderBy: [
              { sortOrder: 'asc' },
              { name: 'asc' }
            ],
            include: {
              _count: {
                select: { products: true }
              }
            }
          },
          _count: {
            select: { products: true }
          }
        }
      });

      if (!category) {
        return next(new AppError('Catégorie non trouvée', 404, 'CATEGORY_NOT_FOUND'));
      }

      if (!category.isActive) {
        return next(new AppError('Catégorie non disponible', 404, 'CATEGORY_UNAVAILABLE'));
      }

      res.json({
        status: 'success',
        data: { category }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Get products in a category
  // @route   GET /api/categories/:identifier/products
  // @access  Public
  async getCategoryProducts(req, res, next) {
    try {
      const { identifier } = req.params;
      const {
        page = 1,
        limit = 12,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        includeSubcategories = true
      } = req.query;

      const skip = (parseInt(page) - 1) * parseInt(limit);
      const take = parseInt(limit);

      // Find category
      const isId = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(identifier);
      const categoryWhere = isId ? { id: identifier } : { slug: identifier };

      const category = await prisma.category.findUnique({
        where: categoryWhere,
        include: {
          children: includeSubcategories === 'true' ? {
            select: { id: true }
          } : false
        }
      });

      if (!category) {
        return next(new AppError('Catégorie non trouvée', 404, 'CATEGORY_NOT_FOUND'));
      }

      // Build category IDs to search in
      const categoryIds = [category.id];
      if (includeSubcategories === 'true' && category.children) {
        categoryIds.push(...category.children.map(child => child.id));
      }

      // Build where clause
      const where = {
        status: 'ACTIVE',
        categoryId: { in: categoryIds }
      };

      // Build orderBy clause
      const orderBy = {};
      orderBy[sortBy] = sortOrder;

      // Get products
      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          skip,
          take,
          orderBy,
          include: {
            category: {
              select: { id: true, name: true, slug: true }
            },
            variants: {
              select: {
                id: true,
                price: true,
                comparePrice: true,
                stock: true,
                isDefault: true
              },
              orderBy: { isDefault: 'desc' }
            },
            images: {
              select: { id: true, url: true, altText: true },
              orderBy: { sortOrder: 'asc' },
              take: 1
            },
            reviews: {
              select: { rating: true }
            }
          }
        }),
        prisma.product.count({ where })
      ]);

      // Calculate average rating for each product
      const productsWithRating = products.map(product => {
        const ratings = product.reviews.map(r => r.rating);
        const avgRating = ratings.length > 0 
          ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length 
          : 0;
        
        return {
          ...product,
          avgRating: Math.round(avgRating * 10) / 10,
          reviewCount: ratings.length,
          reviews: undefined
        };
      });

      const totalPages = Math.ceil(total / take);

      res.json({
        status: 'success',
        data: {
          category: {
            id: category.id,
            name: category.name,
            slug: category.slug,
            description: category.description
          },
          products: productsWithRating,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalItems: total,
            itemsPerPage: take,
            hasNextPage: parseInt(page) < totalPages,
            hasPrevPage: parseInt(page) > 1
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Create new category
  // @route   POST /api/categories
  // @access  Private (Admin/Employee)
  async createCategory(req, res, next) {
    try {
      const {
        name,
        description,
        image,
        parentId,
        sortOrder = 0,
        isActive = true
      } = req.body;

      // Generate slug
      const slug = slugify(name, { lower: true, strict: true });
      
      // Check if slug already exists
      const existingCategory = await prisma.category.findUnique({
        where: { slug }
      });

      if (existingCategory) {
        return next(new AppError('Une catégorie avec ce nom existe déjà', 409, 'CATEGORY_EXISTS'));
      }

      // Validate parent category if provided
      if (parentId) {
        const parentCategory = await prisma.category.findUnique({
          where: { id: parentId }
        });

        if (!parentCategory) {
          return next(new AppError('Catégorie parent non trouvée', 404, 'PARENT_CATEGORY_NOT_FOUND'));
        }
      }

      // Create category
      const category = await prisma.category.create({
        data: {
          name,
          slug,
          description,
          image,
          parentId,
          sortOrder,
          isActive
        },
        include: {
          parent: {
            select: { id: true, name: true, slug: true }
          },
          _count: {
            select: { products: true }
          }
        }
      });

      logger.logBusiness('Category created', {
        categoryId: category.id,
        name: category.name,
        parentId,
        userId: req.user.id
      });

      res.status(201).json({
        status: 'success',
        message: 'Catégorie créée avec succès',
        data: { category }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Update category
  // @route   PUT /api/categories/:id
  // @access  Private (Admin/Employee)
  async updateCategory(req, res, next) {
    try {
      const { id } = req.params;
      const updateData = { ...req.body };

      // Update slug if name changed
      if (updateData.name) {
        updateData.slug = slugify(updateData.name, { lower: true, strict: true });
      }

      // Validate parent category if provided
      if (updateData.parentId) {
        // Check if trying to set self as parent
        if (updateData.parentId === id) {
          return next(new AppError('Une catégorie ne peut pas être son propre parent', 400, 'INVALID_PARENT'));
        }

        const parentCategory = await prisma.category.findUnique({
          where: { id: updateData.parentId }
        });

        if (!parentCategory) {
          return next(new AppError('Catégorie parent non trouvée', 404, 'PARENT_CATEGORY_NOT_FOUND'));
        }

        // Check for circular reference
        const isCircular = await this.checkCircularReference(id, updateData.parentId);
        if (isCircular) {
          return next(new AppError('Référence circulaire détectée', 400, 'CIRCULAR_REFERENCE'));
        }
      }

      const category = await prisma.category.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date()
        },
        include: {
          parent: {
            select: { id: true, name: true, slug: true }
          },
          children: {
            select: { id: true, name: true, slug: true }
          },
          _count: {
            select: { products: true }
          }
        }
      });

      logger.logBusiness('Category updated', {
        categoryId: category.id,
        name: category.name,
        changes: updateData,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: 'Catégorie mise à jour avec succès',
        data: { category }
      });

    } catch (error) {
      next(error);
    }
  }

  // @desc    Delete category
  // @route   DELETE /api/categories/:id
  // @access  Private (Admin)
  async deleteCategory(req, res, next) {
    try {
      const { id } = req.params;

      // Check if category exists
      const category = await prisma.category.findUnique({
        where: { id },
        include: {
          children: true,
          _count: {
            select: { products: true }
          }
        }
      });

      if (!category) {
        return next(new AppError('Catégorie non trouvée', 404, 'CATEGORY_NOT_FOUND'));
      }

      // Check if category has products
      if (category._count.products > 0) {
        return next(new AppError('Impossible de supprimer une catégorie contenant des produits', 400, 'CATEGORY_HAS_PRODUCTS'));
      }

      // Check if category has children
      if (category.children.length > 0) {
        return next(new AppError('Impossible de supprimer une catégorie ayant des sous-catégories', 400, 'CATEGORY_HAS_CHILDREN'));
      }

      // Delete category
      await prisma.category.delete({
        where: { id }
      });

      logger.logBusiness('Category deleted', {
        categoryId: category.id,
        name: category.name,
        userId: req.user.id
      });

      res.json({
        status: 'success',
        message: 'Catégorie supprimée avec succès'
      });

    } catch (error) {
      next(error);
    }
  }

  // Helper method to check circular reference
  async checkCircularReference(categoryId, parentId) {
    let currentParentId = parentId;
    
    while (currentParentId) {
      if (currentParentId === categoryId) {
        return true; // Circular reference found
      }
      
      const parent = await prisma.category.findUnique({
        where: { id: currentParentId },
        select: { parentId: true }
      });
      
      currentParentId = parent?.parentId;
    }
    
    return false; // No circular reference
  }
}

module.exports = new CategoryController();

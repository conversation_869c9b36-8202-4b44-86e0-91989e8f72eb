const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const { 
  generateToken, 
  hashPassword, 
  comparePassword, 
  authenticate,
  authRateLimit,
  validatePassword
} = require('../middleware/auth');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const emailService = require('../services/emailService');

const router = express.Router();
const prisma = new PrismaClient();

// Apply rate limiting to auth routes
router.use(rateLimit(authRateLimit));

// Validation middleware
const validateRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email invalide'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Le mot de passe doit contenir au moins 8 caractères'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le prénom doit contenir entre 2 et 50 caractères'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le nom doit contenir entre 2 et 50 caractères'),
  body('phone')
    .optional()
    .isMobilePhone('ar-TN')
    .withMessage('Numéro de téléphone invalide')
];

const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email invalide'),
  body('password')
    .notEmpty()
    .withMessage('Mot de passe requis')
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    return next(new AppError(errorMessages.join(', '), 400, 'VALIDATION_ERROR'));
  }
  next();
};

// @route   POST /api/auth/register
// @desc    Register new user
// @access  Public
router.post('/register', validateRegistration, handleValidationErrors, async (req, res, next) => {
  try {
    const { email, password, firstName, lastName, phone } = req.body;

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return next(new AppError(passwordValidation.errors.join(', '), 400, 'WEAK_PASSWORD'));
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      logger.logSecurity('Registration attempt with existing email', {
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      return next(new AppError('Un compte avec cet email existe déjà', 409, 'EMAIL_EXISTS'));
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        firstName,
        lastName,
        phone,
        role: 'CLIENT',
        status: 'ACTIVE',
        isVerified: false
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        role: true,
        status: true,
        isVerified: true,
        createdAt: true
      }
    });

    // Send verification email
    try {
      await emailService.sendVerificationEmail(user.email, verificationToken, user.firstName);
    } catch (emailError) {
      logger.error('Failed to send verification email', { 
        userId: user.id, 
        email: user.email, 
        error: emailError.message 
      });
      // Don't fail registration if email fails
    }

    // Generate JWT token
    const token = generateToken({ 
      id: user.id, 
      email: user.email, 
      role: user.role 
    });

    logger.logBusiness('User registered', {
      userId: user.id,
      email: user.email,
      role: user.role,
      ip: req.ip
    });

    res.status(201).json({
      status: 'success',
      message: 'Compte créé avec succès. Vérifiez votre email pour activer votre compte.',
      data: {
        user,
        token
      }
    });

  } catch (error) {
    next(error);
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', validateLogin, handleValidationErrors, async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        firstName: true,
        lastName: true,
        phone: true,
        role: true,
        status: true,
        isVerified: true,
        lastLoginAt: true
      }
    });

    if (!user) {
      logger.logSecurity('Login attempt with non-existent email', {
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      return next(new AppError('Email ou mot de passe incorrect', 401, 'INVALID_CREDENTIALS'));
    }

    // Check password
    const isPasswordValid = await comparePassword(password, user.password);
    if (!isPasswordValid) {
      logger.logSecurity('Login attempt with wrong password', {
        userId: user.id,
        email: user.email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      return next(new AppError('Email ou mot de passe incorrect', 401, 'INVALID_CREDENTIALS'));
    }

    // Check if account is active
    if (user.status !== 'ACTIVE') {
      logger.logSecurity('Login attempt with inactive account', {
        userId: user.id,
        email: user.email,
        status: user.status,
        ip: req.ip
      });
      return next(new AppError('Compte désactivé', 401, 'ACCOUNT_INACTIVE'));
    }

    // Remove password from response
    delete user.password;

    // Generate JWT token
    const token = generateToken({ 
      id: user.id, 
      email: user.email, 
      role: user.role 
    });

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    });

    logger.logBusiness('User logged in', {
      userId: user.id,
      email: user.email,
      role: user.role,
      ip: req.ip
    });

    res.json({
      status: 'success',
      message: 'Connexion réussie',
      data: {
        user,
        token
      }
    });

  } catch (error) {
    next(error);
  }
});

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', authenticate, async (req, res, next) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        avatar: true,
        role: true,
        status: true,
        isVerified: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true
      }
    });

    res.json({
      status: 'success',
      data: { user }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { toast } from 'react-hot-toast';
import type { CartItem, Product, ProductVariant } from '@/types';

interface CartState {
  // State
  items: CartItem[];
  isOpen: boolean;
  isLoading: boolean;

  // Computed values
  itemCount: number;
  subtotal: number;

  // Actions
  addItem: (product: Product, variant: ProductVariant, quantity?: number) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  toggleCart: () => void;
  openCart: () => void;
  closeCart: () => void;
  
  // Utility functions
  getItem: (productId: string, variantId: string) => CartItem | undefined;
  isInCart: (productId: string, variantId: string) => boolean;
  getItemQuantity: (productId: string, variantId: string) => number;
}

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      // Initial state
      items: [],
      isOpen: false,
      isLoading: false,

      // Computed values
      get itemCount() {
        return get().items.reduce((total, item) => total + item.quantity, 0);
      },

      get subtotal() {
        return get().items.reduce((total, item) => {
          return total + (item.variant.price * item.quantity);
        }, 0);
      },

      // Add item to cart
      addItem: (product: Product, variant: ProductVariant, quantity = 1) => {
        const { items } = get();
        
        // Check if item already exists
        const existingItemIndex = items.findIndex(
          item => item.productId === product.id && item.variantId === variant.id
        );

        if (existingItemIndex >= 0) {
          // Update existing item quantity
          const updatedItems = [...items];
          const existingItem = updatedItems[existingItemIndex];
          const newQuantity = existingItem.quantity + quantity;
          
          // Check stock availability
          if (newQuantity > variant.stock) {
            toast.error(`Stock insuffisant. Seulement ${variant.stock} disponible(s)`);
            return;
          }
          
          updatedItems[existingItemIndex] = {
            ...existingItem,
            quantity: newQuantity
          };
          
          set({ items: updatedItems });
          toast.success('Quantité mise à jour dans le panier');
        } else {
          // Check stock availability
          if (quantity > variant.stock) {
            toast.error(`Stock insuffisant. Seulement ${variant.stock} disponible(s)`);
            return;
          }
          
          // Add new item
          const newItem: CartItem = {
            id: `${product.id}-${variant.id}`,
            productId: product.id,
            variantId: variant.id,
            quantity,
            product,
            variant
          };
          
          set({ items: [...items, newItem] });
          toast.success('Produit ajouté au panier');
        }
      },

      // Remove item from cart
      removeItem: (itemId: string) => {
        const { items } = get();
        const updatedItems = items.filter(item => item.id !== itemId);
        
        set({ items: updatedItems });
        toast.success('Produit retiré du panier');
      },

      // Update item quantity
      updateQuantity: (itemId: string, quantity: number) => {
        if (quantity <= 0) {
          get().removeItem(itemId);
          return;
        }

        const { items } = get();
        const itemIndex = items.findIndex(item => item.id === itemId);
        
        if (itemIndex >= 0) {
          const item = items[itemIndex];
          
          // Check stock availability
          if (quantity > item.variant.stock) {
            toast.error(`Stock insuffisant. Seulement ${item.variant.stock} disponible(s)`);
            return;
          }
          
          const updatedItems = [...items];
          updatedItems[itemIndex] = {
            ...item,
            quantity
          };
          
          set({ items: updatedItems });
        }
      },

      // Clear entire cart
      clearCart: () => {
        set({ items: [] });
        toast.success('Panier vidé');
      },

      // Toggle cart visibility
      toggleCart: () => {
        set(state => ({ isOpen: !state.isOpen }));
      },

      // Open cart
      openCart: () => {
        set({ isOpen: true });
      },

      // Close cart
      closeCart: () => {
        set({ isOpen: false });
      },

      // Get specific item
      getItem: (productId: string, variantId: string) => {
        const { items } = get();
        return items.find(
          item => item.productId === productId && item.variantId === variantId
        );
      },

      // Check if item is in cart
      isInCart: (productId: string, variantId: string) => {
        return !!get().getItem(productId, variantId);
      },

      // Get item quantity
      getItemQuantity: (productId: string, variantId: string) => {
        const item = get().getItem(productId, variantId);
        return item?.quantity || 0;
      }
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({
        items: state.items
      })
    }
  )
);

// Cart selectors and hooks
export const useCart = () => {
  const store = useCartStore();
  return {
    items: store.items,
    itemCount: store.itemCount,
    subtotal: store.subtotal,
    isOpen: store.isOpen,
    isLoading: store.isLoading,
    addItem: store.addItem,
    removeItem: store.removeItem,
    updateQuantity: store.updateQuantity,
    clearCart: store.clearCart,
    toggleCart: store.toggleCart,
    openCart: store.openCart,
    closeCart: store.closeCart,
    getItem: store.getItem,
    isInCart: store.isInCart,
    getItemQuantity: store.getItemQuantity
  };
};

// Cart utilities
export const cartUtils = {
  // Calculate total with tax and shipping
  calculateTotal: (
    subtotal: number, 
    taxRate: number = 0.19, // 19% TVA in Tunisia
    shippingCost: number = 0
  ) => {
    const tax = subtotal * taxRate;
    return subtotal + tax + shippingCost;
  },

  // Format price
  formatPrice: (price: number, currency: string = 'TND') => {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(price);
  },

  // Check if cart qualifies for free shipping
  qualifiesForFreeShipping: (subtotal: number, threshold: number = 100) => {
    return subtotal >= threshold;
  },

  // Calculate shipping cost
  calculateShipping: (subtotal: number, freeShippingThreshold: number = 100, standardCost: number = 7) => {
    return cartUtils.qualifiesForFreeShipping(subtotal, freeShippingThreshold) ? 0 : standardCost;
  },

  // Validate cart before checkout
  validateCart: (items: CartItem[]) => {
    const errors: string[] = [];
    
    if (items.length === 0) {
      errors.push('Le panier est vide');
    }
    
    items.forEach(item => {
      if (item.quantity > item.variant.stock) {
        errors.push(`Stock insuffisant pour ${item.product.name}`);
      }
      
      if (item.variant.price <= 0) {
        errors.push(`Prix invalide pour ${item.product.name}`);
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

export default useCartStore;

import React from 'react';
import { useParams } from 'react-router-dom';

const ProductDetailPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();

  return (
    <div className="min-h-screen py-8">
      <div className="container-custom">
        <div className="bg-white rounded-lg shadow-soft p-8 text-center">
          <h1 className="text-2xl font-bold mb-4">Détail du Produit</h1>
          <p className="text-secondary-600 mb-4">
            Produit: {slug}
          </p>
          <p className="text-secondary-600">
            Cette page sera bientôt disponible avec tous les détails du produit.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;

const { PrismaClient } = require('@prisma/client');

// Test database setup
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL_TEST || 'postgresql://test:test@localhost:5432/sassoufashion_test'
    }
  }
});

// Global test setup
beforeAll(async () => {
  // Connect to test database
  await prisma.$connect();
  
  // Run migrations
  const { execSync } = require('child_process');
  execSync('npx prisma migrate deploy', { 
    env: { 
      ...process.env, 
      DATABASE_URL: process.env.DATABASE_URL_TEST 
    } 
  });
});

// Clean up after each test
afterEach(async () => {
  // Clean up test data
  const tablenames = await prisma.$queryRaw`
    SELECT tablename FROM pg_tables WHERE schemaname='public'
  `;
  
  for (const { tablename } of tablenames) {
    if (tablename !== '_prisma_migrations') {
      await prisma.$executeRawUnsafe(`TRUNCATE TABLE "public"."${tablename}" CASCADE;`);
    }
  }
});

// Global test teardown
afterAll(async () => {
  await prisma.$disconnect();
});

// Make prisma available globally in tests
global.prisma = prisma;

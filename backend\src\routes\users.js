const express = require('express');
const { body, param, query } = require('express-validator');
const { authenticate, authorize, validatePassword } = require('../middleware/auth');
const userController = require('../controllers/userController');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Validation middleware
const validateProfileUpdate = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le prénom doit contenir entre 2 et 50 caractères'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le nom doit contenir entre 2 et 50 caractères'),
  body('phone')
    .optional()
    .isMobilePhone('ar-TN')
    .withMessage('Numéro de téléphone invalide'),
  body('avatar')
    .optional()
    .isURL()
    .withMessage('URL d\'avatar invalide')
];

const validatePasswordChange = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Mot de passe actuel requis'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Le nouveau mot de passe doit contenir au moins 8 caractères')
    .custom((value) => {
      const validation = validatePassword(value);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }
      return true;
    })
];

const validateAddress = [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le prénom doit contenir entre 2 et 50 caractères'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le nom doit contenir entre 2 et 50 caractères'),
  body('address1')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('L\'adresse doit contenir entre 5 et 200 caractères'),
  body('city')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('La ville doit contenir entre 2 et 100 caractères'),
  body('state')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('La région doit contenir entre 2 et 100 caractères'),
  body('postalCode')
    .trim()
    .isLength({ min: 4, max: 10 })
    .withMessage('Code postal invalide'),
  body('phone')
    .optional()
    .isMobilePhone('ar-TN')
    .withMessage('Numéro de téléphone invalide')
];

const validateId = [
  param('id')
    .isUUID()
    .withMessage('ID invalide')
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Numéro de page invalide'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite invalide (1-100)')
];

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    return next(new AppError(errorMessages.join(', '), 400, 'VALIDATION_ERROR'));
  }
  next();
};

// All routes require authentication
router.use(authenticate);

// Profile routes
router.get('/profile', userController.getProfile);
router.put('/profile', validateProfileUpdate, handleValidationErrors, userController.updateProfile);
router.post('/change-password', validatePasswordChange, handleValidationErrors, userController.changePassword);

// Address routes
router.get('/addresses', userController.getAddresses);
router.post('/addresses', validateAddress, handleValidationErrors, userController.addAddress);
router.put('/addresses/:id', validateId, validateAddress, handleValidationErrors, userController.updateAddress);
router.delete('/addresses/:id', validateId, handleValidationErrors, userController.deleteAddress);

// Loyalty points routes
router.get('/loyalty-points', validatePagination, handleValidationErrors, userController.getLoyaltyPoints);

// Orders routes
router.get('/orders', validatePagination, handleValidationErrors, userController.getOrders);

// Admin routes
router.get('/', authorize('ADMIN'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, search, role, status } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    const where = {
      ...(search && {
        OR: [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ]
      }),
      ...(role && { role }),
      ...(status && { status })
    };

    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          phone: true,
          role: true,
          status: true,
          isVerified: true,
          lastLoginAt: true,
          createdAt: true,
          _count: {
            select: {
              orders: true,
              reviews: true
            }
          }
        }
      }),
      prisma.user.count({ where })
    ]);

    const totalPages = Math.ceil(total / take);

    res.json({
      status: 'success',
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: take
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
